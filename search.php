<?php get_header(); ?>

    <main id="page" class="default-template">

    <?php

    $s = get_search_query();

    $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;

    // Query for members
    $argsTeam = array( 
        's' => $s,
        'posts_per_page' => 18,
        'post_type' => array('page', 'post', 'team_member', 'more'),
        'relevanssi'  => true,
        'paged' => $paged,
        'tax_query' => array(
            array(
                'taxonomy' => 'category',
                'field'    => 'slug',
                'terms'    => 'uncategorized',
                'operator' => 'NOT IN'
            ),
        ),
    );
    $the_query = new WP_Query( $argsTeam );

    ?>

    <?php if ( $the_query->have_posts() ) : ?>

        <div class="blocks-container">
            <h1 class="search-results"><?php printf( __( 'Search Results for: %s', 'shape' ), '<span>' . get_search_query() . '</span>' ); ?></h1>

                <div class="results-grid">
                    <?php
                    // Display team members
                    while ( $the_query->have_posts() ) : $the_query->the_post(); ?>

                        <?php if (get_post_type() == 'team_member'): ?>

                            <div class="posts-section">

                                <a href="<?php the_permalink(); ?>" class="post-card team-member<?php echo $disabled; ?>">
                                    <div class="post-card-thumbnail">
                                        <?php the_post_thumbnail( 'large' ); ?>
                                    </div>
                                    
                                    <div class="search-result-team-member">
                                        <h3><?php the_title(); ?></h3>
                                    </div>

                                    <div class="post-card-meta">
                                        <?php
                                        $term_types = array('position', 'location', 'service');
                                        foreach ($term_types as $term_type) {
                                            $terms = get_the_terms(get_the_ID(), $term_type);
                                            if ($terms && !is_wp_error($terms)) {
                                                foreach ($terms as $term) {
                                                    ?>
                                                    <div class="member-meta">
                                                        <?php echo $term->name; ?>
                                                        <br>
                                                    </div>
                                                    <?php
                                                }
                                            }
                                        }
                                        ?>

                                        <ul class="search-result-social-media">
                                            <?php if(get_field('linkedin', get_the_ID())): ?>
                                            <li>
                                                <a href="<?php the_field('linkedin', get_the_ID()); ?>" target="_blank">
                                                    <?php theme_asset('img/icon-linkedin.svg'); ?>
                                                </a>
                                            <?php endif; ?>
                                        </ul>

                                        <a href="<?php the_permalink(); ?>" class="team-member-bio-open">
                                            <?php _e('Read Bio', 'mm-smy'); ?>
                                        </a>

                                    </div>
                                </a>

                            </div>

                        <?php else : ?>
                    
                            <div class="posts-section">

                                <a href="<?php the_permalink(); ?>" class="post-card <?php echo $disabled; ?>">
                                    <div class="post-card-thumbnail">
                                        <?php the_post_thumbnail(); ?>
                                    </div>
                                    <div class="post-card-meta">
                                        <?php
                                            $content_types = get_the_terms(get_the_ID(), 'industry');
                                            if (!empty($content_types)) {
                                                $main_content_type = $content_types[0]; // Get the first term
                                        ?>
                                            <div class="content-type">
                                                <?php echo $main_content_type->name; ?>
                                            </div>
                                            <time><?php echo get_the_date(); ?></time>
                                        <?php
                                            }
                                        ?>
                                    </div>
                                    <h3><?php the_title(); ?></h3>
                                    <?php if($body): ?>
                                        <p class="project-body"><?php echo $body; ?></p>
                                    <?php endif; ?>
                                </a>
                                
                            </div>

                        <?php endif; ?>

                    <?php endwhile; wp_reset_postdata() ?>

                </div>

                <?php if(!is_singular() || $the_query->found_posts >= 18): ?>
                    <div class="pagination">
                        <div class="nav-links">
                            <?php
                                echo paginate_links( array(
                                    'base' => str_replace( PHP_INT_MAX, '%#%', esc_url( get_pagenum_link( PHP_INT_MAX ) ) ),
                                    'format' => '?paged=%#%',
                                    'total' => $the_query->max_num_pages,
                                    'prev_text' => __('Previous', 'mm-smy'),
                                    'next_text' => __('Next', 'mm-smy')
                                ) );
                            ?>
                        </div>
                    </div>
                <?php endif; ?>
        </div>

        <?php else : ?>

            <div class="blocks-container">
                <div class="search-results">
                    <h3>No Results Found</h3>
                </div>
            </div>

    <?php endif; ?>

    </main>

<?php get_footer(); ?>
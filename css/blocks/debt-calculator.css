.debt-calculator{
    margin: var(--section-spacing) auto;
    overflow: hidden;
}

.debt-calculator h2{
    font: var(--font-headline-3-semibold);
    letter-spacing: var(--font-headline-3--spacing);
    margin-bottom: 6rem;
}

.debt-calculator h2.animated{
    animation-name: fadeIn;
}

.debt-calculator-inner{
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    column-gap: 4rem;
    row-gap: 4rem;
}

.debt-calculator-intro.animated{
    animation-name: fadeIn;
}

.debt-calculator-main.animated{
    animation-name: fadeIn;
}

.debt-calculator-form form{
    display: grid;
    column-gap: 3rem;
    row-gap: 2rem;
}

.debt-calculator-form label{
    font: var(--font-button-semibold);
    color: var(--color-warm-grey);
    text-transform: uppercase;
    display: block;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.debt-calculator-form label button{
    font: var(--font-breadcrumbs-semibold);
    letter-spacing: var(--font-breadcrumbs--spacing);
    text-transform: uppercase;
    text-decoration: none;
    border: none;
    border-radius: 50%;
    padding: 0 0 0 0.2rem;
    width: 2.4rem;
    height: 2.4rem;
    appearance: none;
    display: inline-block;
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
    color: var(--color-grey);
    background-color: var(--color-yellow);
}

.debt-calculator-form label button:is(:hover, :focus, :active){
    background-color: var(--color-grey);
    color: var(--color-yellow);
    text-decoration: none;
    outline: none;
}

.debt-calculator-form .form-help{
    font: var(--font-body-small-regular);
    letter-spacing: var(--font-body-small--spacing);
    margin-top: 0.5rem;
    color: var(--color-warm-grey);
    display: none;
}

.debt-calculator-form input:is([type="text"], [type="number"]){
    width: 100%;
    color: var(--color-grey);
    padding: 1rem;
    border-radius: 1.2rem;
    border: 0.1rem solid var(--color-warm-grey);
    background-color: var(--color-white);
    transition: all 0.3s ease;
}

.debt-calculator-form input:is([type="text"], [type="number"])::placeholder{
    font-style: italic;
    color: var(--color-warm-grey);
}

.debt-calculator-form input:is([type="text"], [type="number"]):is(:focus){
    border-color: var(--color-navy);
    outline: none;
}

.debt-calculator-form .radio-group{
    display: flex;
    column-gap: 1.5rem;
    row-gap: 1.5rem;
}

.debt-calculator-form .radio-label{
    display: block;
    margin-bottom: 0;
    overflow: hidden;
    position: relative;
}

.debt-calculator-form .radio-label>input{
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%, 0);
    opacity: 0;
}

.debt-calculator-form .radio-label>div{
    font: var(--font-breadcrumbs-semibold);
    letter-spacing: var(--font-breadcrumbs--spacing);
    text-transform: uppercase;
    text-decoration: none;
    border: 0.1rem solid var(--color-blue);
    border-radius: 1.2rem;
    padding: 1.2rem 2.4rem;
    appearance: none;
    display: inline-block;
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
    color: var(--color-blue);
    background-color: transparent;
}

.debt-calculator-form .radio-label>div:is(:hover, :focus, :active){
    background-color: var(--color-grey);
    border-color: var(--color-grey);
    color: var(--color-yellow);
    text-decoration: none;
    outline: none;
}

.debt-calculator-form .radio-label>input:checked~div:is(*, :hover, :focus, :active){
    background-color: var(--color-blue);
    border-color: var(--color-blue);
    color: var(--color-white);
}

.debt-calculator-form .form-actions{
    text-align: center;
    margin-top: 2rem;
}

.debt-calculator-results{
    display: none;
}

.debt-calculator-logo{
    display: none;
}

.debt-calculator-results-graphic{
    display: grid;
    grid-template-columns: repeat(5, minmax(0, 1fr));
    grid-template-rows: 20rem auto;
    column-gap: 1rem;
    row-gap: 0.5rem;
    max-width: 55rem;
    background-color: var(--color-light-blue);
    border: 0.1rem solid var(--color-blue);
    border-radius: 1.2rem;
    padding: 2.5rem;
    margin: 0 auto 5rem;
    position: relative;
}

.debt-calculator-results-bar{
    height: 70%;
    justify-self: center;
    align-self: end;
    background-color: var(--color-yellow);
    width: 100%;
    max-width: 10rem;
    border-radius: 1.2rem 1.2rem 0 0;
    border-bottom: 0.2rem solid var(--color-navy);
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem 0;
}

.debt-calculator-results-bar:hover{
    background-color: var(--color-navy);
}

.debt-calculator-results-bar-total{
    font: 600 0.8rem/120% var(--primary-font);
    color: var(--color-navy);
    text-align: center;
    pointer-events: none;
    max-height: 100%;
    overflow: hidden;
}

.debt-calculator-results-bar:hover .debt-calculator-results-bar-total{
    color: var(--color-white);
}

.debt-calculator-results-bar-description{
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: var(--color-white);
    padding: 2.5rem;
    border-radius: 1.2rem;
    margin-top: -1rem;
    margin-left: 1rem;
    box-shadow: 0px 1.5rem 4rem rgb(0 0 0 / 10%);
    transition: all 0.3s ease;
    opacity: 0;
    pointer-events: none;
}

.debt-calculator-results-bar:hover .debt-calculator-results-bar-description{
    opacity: 1;
}

.debt-calculator-results-bar-description h3{
    font: var(--font-headline-5-semibold);
    letter-spacing: var(--font-headline-5--spacing);
    color: var(--color-navy);
    margin-bottom: 1rem;
}

.debt-calculator-results-bar-description p{
    margin-bottom: 0;
}

.debt-calculator-results-label{
    font: 600 0.5rem/120% var(--primary-font);
    color: var(--color-navy);
    text-align: center;
    text-transform: uppercase;
}

.debt-calculator-results-table:not(:last-child){
    border-bottom: 0.1rem dotted var(--color-grey);
}

.debt-calculator-results-table h3{
    font: var(--font-headline-5-semibold);
    letter-spacing: var(--font-headline-5--spacing);
    color: var(--color-blue);
    margin-top: 3rem;
    margin-bottom: 1rem;
}

.debt-calculator-results-table dl{
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    column-gap: 3rem;
    row-gap: 0.5rem;
    margin-bottom: 3rem;
    align-self: start;
}

.debt-calculator-results-table dl dt{
    font: var(--font-subtitle-semibold);
    letter-spacing: var(--font-subtitle--spacing);
    text-transform: uppercase;
    margin-bottom: 0;
}

.debt-calculator-results-table dl dd{
    color: var(--color-blue);
    text-align: right;
    text-transform: capitalize;
    margin-bottom: 0;
}

.debt-calculator-actions{
    display: flex;
    row-gap: 1rem;
    column-gap: 3rem;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    margin-top: 6rem;
}

.debt-calculator-disclaimer{
    font: var(--font-body-small-regular);
    letter-spacing: var(--font-body-small--spacing);
    margin-top: 6rem;
    color: var(--color-warm-grey);
}

@media (min-width: 768px){
    .debt-calculator-form form{
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .debt-calculator-results-table-grid{
        display: grid;
        grid-template-columns: repeat(2, minmax(0, 1fr));
        column-gap: 3rem;
    }

    .debt-calculator-form .form-actions{
        grid-column: span 2;
    }

    .debt-calculator-results-label{
        font-size: 1rem;
    }

    .debt-calculator-results-bar-total{
        font-size: 1.2rem;
    }

    .debt-calculator-results-table dl{
        grid-template-columns: minmax(0, 2fr) minmax(0, 1fr);
    }
}

@media (min-width: 992px){
    .debt-calculator-inner{
        grid-template-columns: minmax(0, 1fr) minmax(0, 2fr);
    }
}

@media print{
    .calculator-print :is(.header, .footer, .blocks-container>:not(.debt-calculator.area-to-print), .debt-calculator h2, .debt-calculator-intro, .debt-calculator-actions),
    .calculator-print>:is(div:not(.page-wrapper)){
        display: none!important;
    }

    .calculator-print .empty-canvas{
        margin: 0;
    }

    .calculator-print .area-to-print.debt-calculator{
        margin: 0;
    }

    .calculator-print .area-to-print.debt-calculator .container{
        width: 100%;
    }

    .calculator-print .area-to-print .debt-calculator-logo{
        display: block;
        margin-bottom: 5rem;
        width: 18.9rem;
    }

    .calculator-print .debt-calculator-inner{
        grid-template-columns: 1fr;
    }

    .debt-calculator-results-table{
        break-inside: avoid;
    }
}
.text-table-3-columns{
    margin: var(--section-spacing) 0;
}

.text-table-3-columns-intro.animated{
    animation-name: fadeIn;
}

.text-table-3-columns-intro h2{
    font: var(--font-headline-3-semibold);
    letter-spacing: var(--font-headline-3--spacing);
}

.text-table-3-columns-inner{
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    column-gap: 6rem;
}

.text-table-3-columns-table{
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    column-gap: 4rem;
}

.text-table-3-columns-column a {
    color: var(--color-blue);
    text-decoration: none;
}

.text-table-3-columns-column a:is(:hover, :focus, :active) {
    color: var(--color-navy);
    text-decoration: underline;
}

.text-table-3-columns-column:has(.button-light-outline) p {
    border-bottom: 0;
}

.text-table-3-columns-column :is(h3, p){
    margin: 0;
    color: var(--color-grey);
    padding: 2rem 0;
    border-bottom: 0.1rem dashed var(--color-navy);
}

.text-table-3-columns-column :is(h3, p).animated{
    animation-name: fadeInUp;
}

.text-table-3-columns-column h3{
    text-transform: uppercase;
    font: 400 1.2rem/2.4rem var(--primary-font);
    letter-spacing: 0.1em;
}

.text-table-3-columns-column p{
    font: var(--font-body-small-regular);
    letter-spacing: var(--font-body-small--spacing);
}

@media (min-width: 768px){
    .text-table-3-columns-table{
        grid-template-columns: repeat(2, minmax(0, 1fr));
        row-gap: 4rem;
    }

    .text-table-3-columns-column:not(:last-child) p:last-child{
        margin-bottom: 0;
    }

}

@media (min-width: 1200px){
    .text-table-3-columns-inner{
        grid-template-columns: minmax(0, 2fr) minmax(0, 3.055fr)
    }
}
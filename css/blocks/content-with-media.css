.content-with-media{
    margin: var(--section-spacing) 0;
    overflow: hidden;
}

.content-with-media-inner:is([data-media-position="right"], [data-media-position="overflow-right"]) .content-with-media-media.animated{
    animation-name: fadeIn;
}

.content-with-media-inner:is([data-media-position="left"], [data-media-position="overflow-left"]) .content-with-media-media.animated{
    animation-name: fadeIn;
}

.content-with-media-inner:is([data-media-position="right"], [data-media-position="overflow-right"]) .content-with-media-content.animated{
    animation-name: fadeIn;
}

.content-with-media-inner:is([data-media-position="left"], [data-media-position="overflow-left"]) .content-with-media-content.animated{
    animation-name: fadeIn;
}

.content-with-media-inner{
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    column-gap: 7rem;
    row-gap: 3rem;
    align-items: center;
}

.content-with-media-content h2{
    font: var(--font-headline-3-semibold);
    letter-spacing: var(--font-headline-3--spacing);
}

.content-with-media-content a {
    color: var(--color-blue);
    text-decoration: none;
}

.content-with-media-content a:not(.button-light-outline):is(:hover, :focus, :active) {
    color: var(--color-navy);
    text-decoration: underline;
}

.content-with-media-content>*:last-child{
    margin-bottom: 0;
}

.content-with-media-media-container{
    border-radius: 1rem;
    border-bottom: 1rem solid var(--color-teal);
    overflow: hidden;
}

.content-with-media-media-container :is(video, img){
    display: block;
    width: 100%;
    height: auto;
}

.content-with-media-media-container .map-outer{
    min-height: 30rem;
    aspect-ratio: 590 / 640;
    width: 100%;
    overflow: hidden;
}

.content-with-media-media-container .map{
    width: 100%;
    height: calc(100% + 40rem);
    position: relative;
    transform: translateY(-20%);
}

.content-with-media-media-container iframe{
    margin-bottom: -0.6em;
    width: 100%;
    aspect-ratio: 3/2;
}

.content-with-media-list{
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    column-gap: 3rem;
    row-gap: 3rem;
    margin-bottom: 3rem;
}

.content-with-media-list-item a{
    display: block;
    line-height: 1.2;
    text-decoration: none;
}

.content-with-media-list-item a:after{
    content: '';
    display: inline-block;
    width: 1.2rem;
    height: 1.2rem;
    background-image: url('../../img/newsletter-arrow.svg');
    background-size: contain;
    background-repeat: no-repeat;
    margin-left: 0.5rem;
    margin-right: 0.5rem;
    vertical-align: middle;
    transition: all 0.3s ease;
}

.content-with-media-list-item a:is(:hover, :focus, :active):after{
    margin-left: 1rem;
    margin-right: 0;
}

.content-with-media-list-item h3{
    font: var(--font-body-regular-semibold);
    line-height: 1.2;
    letter-spacing: var(--font-body-regular--spacing);
    margin-bottom: 1rem;
    color: var(--color-blue);
    display: inline;
}

.content-with-media-list-item p{
    font: var(--font-body-small-regular);
    letter-spacing: var(--font-body-small--spacing);
}

.content-with-media-list-item :is(p, ul, ol){
    margin-bottom: 0.5rem;
}

.content-with-media-list-item>*:last-child{
    margin-bottom: 0;
}

@media (min-width: 768px){
    .content-with-media-inner{
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .content-with-media-inner:is([data-media-position="right"], [data-media-position="overflow-right"]) .content-with-media-media{
        order: 2;
    }

    .content-with-media-inner:is([data-media-position="right"], [data-media-position="left"]) .content-with-media-content{
        max-width: 45rem;
    }

    .content-with-media-inner[data-media-position="left"] .content-with-media-content{
        margin-left: auto;
    }

    .content-with-media-inner[data-media-position="overflow-left"] .content-with-media-media-container{
        transform: translateX(-7rem);
    }

    .content-with-media-inner[data-media-position="overflow-right"] .content-with-media-media-container{
        transform: translateX(7rem);
    }
}

@media (min-width: 992px){
    .content-with-media-list{
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}
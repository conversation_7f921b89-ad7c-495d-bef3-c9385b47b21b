.text-2-columns{
    margin: var(--section-spacing) 0;
}

.text-2-columns-inner{
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    column-gap: 3rem;
    row-gap: 3rem;
}

.text-2-columns-inner h2{
    font: var(--font-headline-3-semibold);
    letter-spacing: var(--font-headline-3--spacing);
}

.text-2-columns-inner h3{
    font: var(--font-headline-6-semibold);
    letter-spacing: var(--font-headline-6--spacing);
    margin-bottom: 1rem;
}

.text-2-columns-intro.animated,
.text-2-columns-content.animated{
    animation-name: fadeIn;
}

.text-2-columns-content{
    font: var(--font-body-large-regular);
    letter-spacing: var(--font-body-large--spacing);
}

.text-2-columns-content a {
    color: var(--color-blue);
    text-decoration: none;
}

.text-2-columns-content a:is(:hover, :focus, :active) {
    color: var(--color-navy);
    text-decoration: underline;
}

.text-2-columns-content>*:last-child{
    margin-bottom: 0;
}

@media (min-width: 768px){
    .text-2-columns{
        overflow: hidden;
    }

    .text-2-columns-inner{
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .text-2-columns-intro p{
        max-width: 40rem;
    }

    .text-2-columns-intro.animated{
        animation-name: fadeIn;
    }

    .text-2-columns-content.animated{
        animation-name: fadeIn;
    }
}
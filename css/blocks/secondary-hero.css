.secondary-hero-container{
    position: relative;
    z-index: 1;
}

.secondary-hero-container:before{
    content: '';
    display: block;
    position: absolute;
    top: 0;
    right: 50%;
    width: 100vw;
    height: 100%;
    background-color: var(--color-navy);
}

.secondary-hero-inner{
    background-color: var(--color-blue);
    position: relative;
    min-height: 35rem;
    overflow: hidden;
    border-radius: 1rem;
    padding: 5.5rem var(--container-padding);
    color: var(--color-white);
    display: flex;
    justify-content: flex-start;
}

.secondary-hero-inner img{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%!important;
    object-fit: cover;
    object-position: center;
    mix-blend-mode: multiply;
}

.secondary-hero-inner-content{
    position: relative;
}

.secondary-hero-inner ul{
    display: flex;
    flex-wrap: wrap;
    margin: 0 0 3rem;
    padding: 0;
}

.secondary-hero-inner ul li{
    list-style: none;
    display: block;
    font: var(--font-breadcrumbs-regular);
    letter-spacing: var(--font-breadcrumbs--spacing);
    text-transform: uppercase;
}

.secondary-hero-inner ul li:not(:last-child){
    padding-right: 3.6rem;
    position: relative;
}

.secondary-hero-inner ul li:not(:last-child):after{
    content: '';
    display: block;
    width: 0.5rem;
    height: 0.5rem;
    border-left: 0.1rem solid var(--color-yellow);
    border-top: 0.1rem solid var(--color-yellow);
    position: absolute;
    top: 50%;
    right: 1.5rem;
    transform: translateY(-50%) rotateZ(135deg);
}

.secondary-hero-inner ul li a{
    font: var(--font-breadcrumbs-semibold);
    color: var(--color-white);
    text-decoration: none;
}

.secondary-hero-inner ul li a:is(:hover, :focus, :active){
    color: var(--color-yellow);
    text-decoration: underline;
    text-decoration-color: var(--color-yellow);
}

.secondary-hero-inner h1{
    font: var(--font-headline-2-semibold);
    letter-spacing: var(--font-headline-2--spacing);
    color: var(--color-white);
}

.secondary-hero-inner p{
    font: var(--font-body-large-regular);
    letter-spacing: var(--font-body-large--spacing);
    margin-bottom: 0;
}

.secondary-hero-inner p[data-width="short"]{
    max-width: 42rem;
}

.secondary-hero-inner p[data-width="medium"]{
    max-width: 68rem;
}

@media (min-width: 768px){
    .secondary-hero-inner{
        padding: 5.5rem;
    }
}
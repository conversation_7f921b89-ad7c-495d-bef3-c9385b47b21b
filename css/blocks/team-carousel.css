.team-carousel{
    margin: var(--section-spacing) 0;
}

.team-carousel header{
    display: flex;
    align-items: center;
    margin-bottom: 3rem;
}

.team-carousel .team-swiper-navigation{
    margin-left: auto;
}

.team-carousel :is(.team-swiper-prev, .team-swiper-next){
    border: none;
    border-radius: 0;
    background: transparent;
    margin: 0;
    padding: 0;
    appearance: none;
    cursor: pointer;
}

.team-carousel :is(.team-swiper-prev, .team-swiper-next):is(:hover, :focus, :active){
    outline: none;
}

.team-carousel :is(.team-swiper-prev, .team-swiper-next) svg>*{
    transition: all 0.3s ease;
}

.team-carousel :is(.team-swiper-prev, .team-swiper-next):is(:hover, :focus, :active) svg>*{
    stroke: var(--color-black);
}

.team-carousel h2{
    font: var(--font-headline-4-semibold);
    letter-spacing: var(--font-headline-4--spacing);
    margin-bottom: 0;
}

.team-swiper{
    margin-bottom: calc(var(--section-spacing) / -2);
}

.team-swiper .swiper-wrapper{
    padding-left: calc(50% - var(--half-container-width) + var(--container-padding));
    padding-bottom: 6rem;
}

.team-swiper .swiper-slide.team-member-card{
    opacity: 0;
}

.team-swiper .swiper-slide.team-member-card:is(.swiper-slide-prev, .swiper-slide-active){
    transition: opacity 0.3s ease;
}

.team-swiper .swiper-slide.team-member-card.swiper-slide-active,
.team-swiper .swiper-slide.team-member-card.swiper-slide-active ~ .swiper-slide{
    opacity: 1;
}

.team-member-card{
    background: var(--color-white);
    border-radius: 1rem;
    box-shadow: 0px 1.5rem 8rem rgba(0, 0, 0, 0.1);
    text-align: center;
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    column-gap: 3rem;
    row-gap: 2rem;
    align-items: center;
    padding: 2rem;
    width: 50rem;
    height: auto;
    max-width: calc(100% - (3 * var(--container-padding)));
    margin: 0 2rem 2rem 0;
}

.team-member-card-image img{
    width: 100%;
    aspect-ratio: 1;
    border-radius: 50%;
    object-fit: cover;
    object-position: center top;
}

.team-member-card h3{
    font: var(--font-body-large-semibold);
    letter-spacing: var(--font-body-large--spacing);
    margin: 0;
}

.team-member-card p{
    font: var(--font-body-large-regular);
    letter-spacing: var(--font-body-large--spacing);
    margin: 0;
    color: var(--color-navy);
}

.team-member-card a{
    margin-top: 2rem;
    display: inline-block;
    color: var(--color-blue);
    text-decoration: none;
    font: var(--font-menu);
    letter-spacing: var(--font-menu--spacing);
    text-transform: uppercase;
}


.team-member-card a:is(:hover, :focus, :active){
    color: var(--color-navy);
    text-decoration: underline;
}

.team-member-card a:is(:hover, :focus, :active) svg path {
    fill: var(--color-grey);
}

@media (max-width: 767px){
    .team-member-card-image img{
        max-width: 15rem;
    }
}

@media (min-width: 768px){
    .team-member-card{
        text-align: left;
        grid-template-columns: minmax(0, 4fr) minmax(0, 6fr);
    }
}
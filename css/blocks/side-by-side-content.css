.side-by-side-content {
	margin: var(--section-spacing) 0;
}

.side-by-side-content-inner {
	display: grid;
	grid-template-columns: minmax(0, 1fr);
	column-gap: 10rem;
	row-gap: 3rem;
}

.side-by-side-content-inner h2 {
	font: var(--font-headline-3-semibold);
	letter-spacing: var(--font-headline-3--spacing);
}

.side-by-side-content-inner h3 {
	font: var(--font-headline-6-semibold);
	letter-spacing: var(--font-headline-6--spacing);
	margin-bottom: 1rem;
}

.side-by-side-content-intro.animated,
.side-by-side-content-content.animated {
	animation-name: fadeIn;
}

.side-by-side-content-content {
	font: var(--font-body-large-regular);
	letter-spacing: var(--font-body-large--spacing);
}

.side-by-side-content-content a {
	color: var(--color-blue);
	text-decoration: none;
}

.side-by-side-content-content a:is(:hover, :focus, :active) {
	color: var(--color-navy);
	text-decoration: underline;
}

.side-by-side-content-content > *:last-child {
	margin-bottom: 0;
}

.side-by-side-content-right {
	display: flex;
	flex-direction: column;
	row-gap: 3rem;
	justify-content: center;
}

.side-by-side-content-right-item {
	display: grid;
	grid-template-columns: 5rem 1fr;
	column-gap: 2rem;
}

.side-by-side-content-right-item p {
	margin-bottom: 0;
}

.side-by-side-content-right-title {
	display: flex;
	column-gap: 3rem;
	flex-direction: column;
	justify-content: center;
}

.side-by-side-content-right-number h2 {
	margin-bottom: 0;
	opacity: 0.5;
	font: var(--font-headline-2-semibold);
}

.side-by-side-content-inner h3 {
	font: var(--font-headline-6-semibold);
	letter-spacing: var(--font-headline-6--spacing);
	margin-bottom: 0;
}

.side-by-side-content-background {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	max-width: var(--container-width);
	margin: 0 auto;
	z-index: -1;
	height: 100%;
}

.side-by-side-content-background img {
	width: 100%;
	height: 100% !important;
}

@media (min-width: 768px) {
	.side-by-side-content {
		overflow: hidden;
	}

	.side-by-side-content-inner {
		grid-template-columns: repeat(2, minmax(0, 1fr));
	}

	.side-by-side-content-intro p {
		max-width: 40rem;
	}

	.side-by-side-content-intro.animated {
		animation-name: fadeIn;
	}

	.side-by-side-content-content.animated {
		animation-name: fadeIn;
	}

	.side-by-side-content-background img {
		object-fit: contain;
	}
}

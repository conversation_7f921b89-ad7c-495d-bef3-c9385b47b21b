.posts-cards{
    margin: var(--section-spacing) 0;
}

.posts-cards-inner{
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    justify-content: space-between;
}

.posts-cards-inner.animated{
    animation-name: fadeIn;
}

.posts-cards-inner h2{
    font: var(--font-headline-4-semibold);
    letter-spacing: var(--font-headline-4--spacing);
    margin-bottom: 0;
}

.posts-cards-inner h2 .subtitle{
    margin-bottom: 1.8rem;
}

.posts-cards-posts{
    width: 100%;
    margin: 3rem 0;
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    column-gap: 2.4rem;
    row-gap: 3rem;
}

@media (min-width: 992px){
    .posts-cards-inner .button-light-filled{
        order: 2;
        margin-left: auto;
    }

    .posts-cards-posts{
        order: 3;
        margin-bottom: 0;
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .posts-cards-posts[data-posts="2"] .post-card:first-child{
        grid-column: span 2;
    }
}
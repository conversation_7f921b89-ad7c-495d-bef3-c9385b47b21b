.styled-background{
    position: relative;
    z-index: 0;
}

.styled-background-color{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.styled-background-color[data-size="overflow"]{
    top: calc(var(--section-spacing) * -2);
    height: calc(100% + (4 * var(--section-spacing)));
}

.styled-background-color[data-size="boxed"]{
    --element-width: var(--container-width);
    max-width: calc(var(--element-width) - var(--total-container-padding))!important;
    border-radius: 1rem;
    overflow: hidden;
    left: 50%;
    transform: translateX(-50%);
}

.styled-background-color[data-size="boxed"] ~ .blocks-container .container{
    width: calc(var(--container-width) - var(--total-container-padding));
}

.styled-background-color[data-color="blue"]{
    background-color: var(--color-blue);
}

.styled-background-color[data-color="navy"]{
    background-color: var(--color-navy);
}

.styled-background-color[data-color="light-blue"]{
    background-color: var(--color-light-blue);
}

.styled-background-color:where([data-color="blue"], [data-color="navy"], [data-color="light-blue"]) + .blocks-container{
    padding: var(--section-spacing) 0;
}

.styled-background-color:where([data-color="blue"], [data-color="navy"], [data-color="light-blue"]) + .blocks-container.small{
    padding: 6rem 0;
}

.styled-background-color:where([data-color="blue"], [data-color="navy"], [data-color="light-blue"]) + .blocks-container.medium{
    padding: var(--section-spacing) 0;
}

.styled-background-color:where([data-color="blue"], [data-color="navy"], [data-color="light-blue"]) + .blocks-container.large{
    padding: 20rem 0;
}

.styled-background-color:where([data-color="blue"], [data-color="navy"], [data-color="light-blue"]) + .blocks-container>div:first-child{
    margin-top: 0;
}

.styled-background-color:where([data-color="blue"], [data-color="navy"], [data-color="light-blue"]) + .blocks-container>div:last-child{
    margin-bottom: 0;
}

.styled-background-graphic{
    position: absolute;
    z-index: -1;
}

.styled-background-graphic[data-side="left"]{
    left: 0;
}

.styled-background-graphic[data-side="left"] svg{
    width: 47.2rem;
    height: auto;
    max-width: 100%;
}

.styled-background-graphic[data-side="right"]{
    right: 0;
}

.styled-background-graphic[data-position="overflow-top"]{
    top: calc(var(--section-spacing) * -1);
}

.styled-background-graphic[data-position="top"]{
    top: 0;
}

.styled-background-graphic[data-position="center"]{
    top: 50%;
    transform: translateY(-50%);
}

.styled-background-graphic[data-position="bottom"]{
    bottom: 0;
}

.styled-background-graphic[data-position="overflow-bottom"]{
    bottom: calc(var(--section-spacing) * -1);
}

.acf-block-preview :is(.styled-background-color, .styled-background-graphic){
    display: none;
}

@media (min-width: 768px){
    .styled-background-color[data-size="boxed"] ~ .blocks-container .container{
        width: calc(var(--container-width) - 10rem);
    }
}
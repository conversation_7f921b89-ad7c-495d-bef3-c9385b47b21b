.form-cta{
    margin: var(--section-spacing) 0;
    overflow: hidden;
}

.form-cta-inner{
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    column-gap: 3rem;
    row-gap: 3rem;
}

.form-cta-description h2{
    font: var(--font-headline-3-semibold);
    letter-spacing: var(--font-headline-3--spacing);
    margin-bottom: 2rem;
}

.form-cta-description p{
    font: var(--font-body-large-regular);
    letter-spacing: var(--font-body-large--spacing);
    margin-bottom: 0;
}

.blocks-container .form-cta .form-cta-inner .form-cta-description a {
    color: var(--color-yellow);
    text-decoration: none;
}

.blocks-container .form-cta .form-cta-inner .form-cta-description a:is(:hover, :active, :focus) {
    text-decoration: underline;
}

.form-cta-form .gform_heading{
    display: none;
}

.form-cta-form .gform_wrapper.gravity-theme .gfield_required{
    color: inherit;
}

.form-cta-form .gform_wrapper :where(legend, label).gfield_label{
    font-weight: 600 !important;
    text-transform: uppercase;
    font-size: 1.3rem!important;
    color: var(--color-warm-grey);
}

.form-cta-form .gform_wrapper .ginput_container :where(input, select, textarea){
    border: 0.1rem solid var(--color-warm-grey);
    border-radius: 1.2rem;
    transition: all 0.3s ease;
}

.form-cta-form .gform_wrapper .ginput_container :where(input, select, textarea):focus{
    border-color: var(--color-navy);
    outline: none;
}

body .form-cta-form .gform_wrapper .ginput_container select{
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

body .form-cta-form .gform_wrapper .ginput_container_select{
    position: relative;
}

body .form-cta-form .gform_wrapper .ginput_container_select::after{
    content: '';
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 2.2rem;
    margin-block: auto;
    border-bottom: solid 0.2rem var(--color-blue);
    border-right: solid 0.2rem var(--color-blue);
    width: 0.9rem;
    height: 0.9rem;
    transform: rotate(45deg);
}

body .form-cta-form .gform_wrapper .gform_validation_errors{
    background: transparent;
    border: none;
    box-shadow: none;
    padding: 0;
}

body .form-cta-form .gform_wrapper .gform_validation_errors>h2{
    color: var(--color-grey);
}

body .form-cta-form .gform_wrapper .gform_validation_errors>h2 .gform-icon{
    display: none;
}

body .form-cta-form .gform_wrapper .gfield_validation_message,
body .form-cta-form .gform_wrapper .validation_message{
    border: none;
    padding: 0!important;
    background: transparent;
    font-style: italic;
}

.form-cta-description.animated,
.form-cta-form.animated{
    animation-name: fadeIn;
}

:is([data-color="blue"], [data-color="navy"]) + .blocks-container .form-cta-description h2,
:is([data-color="blue"], [data-color="navy"]) + .blocks-container .form-cta-description p,
:is([data-color="blue"], [data-color="navy"]) + .blocks-container .form-cta-description a,
:is([data-color="blue"], [data-color="navy"]) + .blocks-container .form-cta-form .gform_heading,
:is([data-color="blue"], [data-color="navy"]) + .blocks-container .form-cta-form .gform_wrapper .gfield_required,
:is([data-color="blue"], [data-color="navy"]) + .blocks-container .form-cta-form .gform_wrapper :where(legend, label).gfield_label,
:is([data-color="blue"], [data-color="navy"]) + .blocks-container .form-cta-form .gform_confirmation_message,
:is([data-color="blue"], [data-color="navy"]) + .blocks-container .form-cta-form .gform_wrapper .gform_validation_errors>h2,
:is([data-color="blue"], [data-color="navy"]) + .blocks-container .form-cta-form .gform_wrapper.gravity-theme .validation_message{
    color: var(--color-white);
}

:is([data-color="blue"], [data-color="navy"]) + .blocks-container .form-cta-form .gform_wrapper .ginput_container :where(input, select, textarea){
    border-color: var(--color-white);
}

:is([data-color="blue"], [data-color="navy"]) + .blocks-container .form-cta-form .gform_wrapper .ginput_container :where(input, select, textarea):focus{
    border-color: var(--color-yellow);
}

@media (min-width: 992px){
    .form-cta-inner{
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .form-cta-form .gform_footer{
        text-align: right;
        flex-direction: row-reverse;
    }

    .form-cta-form.animated{
        animation-name: fadeIn;
    }
}
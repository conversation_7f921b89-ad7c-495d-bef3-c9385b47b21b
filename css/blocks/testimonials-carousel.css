.testimonials-carousel{
    margin: var(--section-spacing) 0;
    overflow: hidden;
}

.testimonials-carousel-inner{
    display: grid;
    grid-template-columns: minmax(0, 1fr);
}

.testimonials-carousel-inner.animated{
    animation-name: fadeInUp;
}

.testimonials-carousel-image-container{
    position: relative;
}

.testimonials-carousel-title{
    border-radius: 1rem 1rem 0 0;
    background-color: var(--color-navy);
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 10;
    padding: 2.4rem var(--container-padding);
}

.testimonials-carousel-title h2{
    color: var(--color-white);
    font: var(--font-subtitle-semibold);
    letter-spacing: var(--font-subtitle--spacing);
    text-transform: uppercase;
    text-align: center;
    margin: 0;
}

.testimonials-carousel-image-swiper img{
    aspect-ratio: 3 / 2;
    display: block;
    width: 100%;
    border-radius: 1rem;
}

.testimonials-carousel-swiper .swiper-wrapper {
    display: flex;
    align-items: center;
}

.testimonials-carousel-quote-container{
    background-color: var(--color-blue);
    color: var(--color-white);
    padding: calc(var(--section-spacing) / 2) var(--container-padding);
}

.testimonials-carousel-quote-container .swiper-slide{
    background-color: var(--color-blue);
}

.testimonials-carousel-quote-container p{
    font-family: var(--secondary-font);
    font-weight: 500;
    font-size: 2rem;
    line-height: 130%;
    letter-spacing: -0.01em;
    margin-bottom: 3rem;
}

.testimonials-carousel-quote-container [data-font-size="extra-small"] p{
    font-size: 1.6rem;
}

.testimonials-carousel-quote-container [data-font-size="small"] p{
    font-size: 2rem;
}

.testimonials-carousel-quote-container [data-font-size="medium"] p{
    font-size: 2.4rem;
}

.testimonials-carousel-quote-container [data-font-size="medium-large"] p{
    font-size: 2.8rem;
}

.testimonials-carousel-quote-container h3{
    font-family: var(--secondary-font);
    font-weight: 400;
    font-size: 1.2rem;
    line-height: 200%;
    letter-spacing: 0.1em;
    margin: 0;
    color: var(--color-white);
    text-transform: uppercase;
}

.testimonials-carousel-quote-navigation{
    margin-top: 2.8rem;
    display: flex;
    column-gap: 1rem;
}

.testimonials-carousel-quote-next,
.testimonials-carousel-quote-prev{
    border: none;
    border-radius: 0;
    background: transparent;
    margin: 0;
    padding: 0;
    appearance: none;
    cursor: pointer;
}

:is(.testimonials-carousel-quote-next, .testimonials-carousel-quote-prev).swiper-button-disabled{
    opacity: 0.5;
    cursor: default;
}

:is(.testimonials-carousel-quote-next, .testimonials-carousel-quote-prev):is(:hover, :focus, :active){
    outline: none;
}

:is(.testimonials-carousel-quote-next, .testimonials-carousel-quote-prev) svg :is(path, rect) {
    transition: all 0.3s ease;
}

:is(.testimonials-carousel-quote-next, .testimonials-carousel-quote-prev):is(:hover, :focus, :active) svg path {
    fill: var(--color-yellow);
}

:is(.testimonials-carousel-quote-next, .testimonials-carousel-quote-prev):is(:hover, :focus, :active) svg rect {
    stroke: var(--color-yellow);
}

@media (max-width: 991px){
    .testimonials-carousel>.container{
        padding: 0;
    }
}

@media (min-width: 992px){
    .testimonials-carousel-inner{
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .testimonials-carousel-inner[data-image-position="left"] .testimonials-carousel-title{
        top: 0;
        left: auto;
        right: 0;
        border-radius: 1rem 0 0 1rem;
        width: 7.2rem;
        margin: 5.3rem 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .testimonials-carousel-inner[data-image-position="left"] .testimonials-carousel-title h2{
        transform: rotateZ(-90deg);
    }

    .testimonials-carousel-inner[data-image-position="right"] .testimonials-carousel-title{
        top: 0;
        left: 0;
        border-radius: 0 1rem 1rem 0;
        width: 7.2rem;
        margin: 5.3rem 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .testimonials-carousel-inner[data-image-position="right"] .testimonials-carousel-title h2{
        transform: rotateZ(90deg);
    }

    /*.testimonials-carousel-inner[data-image-position="left"] .testimonials-carousel-image-container{
        width: 50vw;
        position: relative;
        left: 50%;
        margin-left: calc(50% - 50vw);
    }*/

    .testimonials-carousel-inner[data-image-position="right"] .testimonials-carousel-image-container{
        order: 2;
        /*width: 50vw;
        position: relative;*/
        left: 0;
    }

    .testimonials-carousel-image-swiper{
        height: 100%;
    }

    .testimonials-carousel-image-swiper img{
        min-height: 100%;
        object-fit: cover;
        object-position: center;
    }

    .testimonials-carousel-quote-container{
        margin: auto 0;
        position: relative;
    }

    .testimonials-carousel-inner[data-image-position="left"] .testimonials-carousel-quote-container {
        padding: 3rem 0rem 3rem 6rem;
    }

    .testimonials-carousel-inner[data-image-position="left"] .testimonials-carousel-image-swiper img {
        border-radius: 1rem 0 0 1rem;
    }

    .testimonials-carousel-inner[data-image-position="right"] .testimonials-carousel-quote-container {
        padding: 3rem 6rem 3rem 0;
    }

    .testimonials-carousel-inner[data-image-position="right"] .testimonials-carousel-image-swiper img {
        border-radius: 0rem 1rem 1rem 0rem;
    }

    .testimonials-carousel-inner[data-image-position="left"] .testimonials-carousel-quote-container:before{
        content: '';
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100%;
        background: var(--color-blue);
        z-index: -1;
    }

    .testimonials-carousel-inner[data-image-position="right"] .testimonials-carousel-quote-container:before{
        content: '';
        display: block;
        position: absolute;
        top: 0;
        right: 0;
        width: 100vw;
        height: 100%;
        background: var(--color-blue);
        z-index: -1;
    }

    .testimonials-carousel-quote-container p{
        font-size: 2rem;
    }

    .testimonials-carousel-quote-container [data-font-size="extra-small"] p{
        font-size: 2rem;
    }

    .testimonials-carousel-quote-container [data-font-size="small"] p{
        font-size: 2rem;
    }

    .testimonials-carousel-quote-container [data-font-size="medium"] p{
        font-size: 2rem;
    }

    .testimonials-carousel-quote-container [data-font-size="medium-large"] p{
        font-size: 2rem;
    }
}

@media (min-width: 1200px) {
    .testimonials-carousel-inner[data-image-position="left"] .testimonials-carousel-image-swiper img {
        border-radius: 1rem;
    }

    .testimonials-carousel-inner[data-image-position="right"] .testimonials-carousel-image-swiper img {
        border-radius: 1rem;
    }
}
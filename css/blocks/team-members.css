.team-members{
    margin: var(--section-spacing) 0;
}

.team-members-filter{
    margin-bottom: calc(var(--section-spacing) / 2);
    display: flex;
    flex-wrap: wrap;
    row-gap: 1.5rem;
    column-gap: 3rem;
}

.team-members-filter select{
    border: 0.2rem solid var(--color-blue);
    border-radius: 1.2rem;
    padding: 1.1rem 7rem 1.1rem 3rem;
    font-weight: 600;
    color: var(--color-navy);
    font-size: 1.6rem;
    transition: all 0.3s ease;
    background: transparent;
    color: var(--color-navy);
    max-width: 100%;
    width: 100%;
}

.team-members-filter select:focus{
    border-color: var(--color-yellow);
}

.team-members-search-wrapper {
    display: flex;
    align-items: center;
    border: 0.2rem solid var(--color-blue);
    border-radius: 1.2rem;
    padding: 0 3rem 0 1.5rem;
    background: transparent;
    width: 100%;
    transition: all 0.3s ease;
}

.team-members-search-wrapper:focus-within {
    border-color: var(--color-yellow);
}

.team-members-search-icon {
    display: flex;
    background-color: var(--color-navy);
    -webkit-mask-image: url('../../img/icon-search.svg');
    mask-image: url('../../img/icon-search.svg');
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-size: contain;
    mask-size: contain;
    width: 1.9rem;
    height: 2rem;
    margin-right: 1.2rem;
    flex-shrink: 0;
}

.team-members-search-wrapper .team-members-search-input {
    border: none;
    background: transparent;
    font-weight: 600;
    color: var(--color-navy);
    font-size: 1.6rem;
    padding: 1.1rem 0;
    width: 100%;
    outline: none;
}

.team-members-search-input::placeholder {
    color: var(--color-navy);
}

.team-members-loading{
    margin: 6rem 0;
    display: none;
}

.team-members-loading svg{
    display: block;
    margin: 0 auto;
    width: 20rem;
    height: auto;
}

.team-members-inner{
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    column-gap: 3rem;
    row-gap: 6rem;
}

.team-member-full-card-image{
    border-radius: 1rem 1rem 0.5rem 0.5rem;
    border-bottom: 0.5rem solid var(--color-teal);
    overflow: hidden;
    margin-bottom: 2rem;
}

.team-member-full-card-image img{
    width: 100%;
    aspect-ratio: 292 / 368;
    object-fit: cover;
    object-position: center top;
}

.team-member-full-card h3{
    font: var(--font-body-large-semibold);
    letter-spacing: var(--font-body-large--spacing);
    margin: 0;
}

.team-member-full-card p{
    margin: 0;
}

.team-member-full-card .social-media{
    margin: 2rem 0;
}

.team-member-full-card .social-media li a svg path{
    transition: all 0.3s ease;
}

.team-member-full-card .social-media li a:is(:hover, :focus, :active) svg path{
    fill: var(--color-navy);
}

.team-member-full-card .team-member-bio-open{
    border: none;
    border-radius: 0;
    padding: 0;
    margin: 0;
    appearance: none;
    background: transparent;
    cursor: pointer;
    font: var(--font-button-semibold);
    letter-spacing: var(--font-button--spacing);
    text-transform: uppercase;
    color: var(--color-grey);
    transition: all 0.3s ease;
    text-decoration: none;
    text-decoration-color: var(--color-blue);
    text-underline-offset: 0.2em;    
}

.team-member-full-card .team-member-bio-open:is(:hover, :focus, :active){
    color: var(--color-blue);
    outline: none;
    text-decoration: underline;
}

.team-member-modal-open{
    overflow: hidden;
}

.team-member-bio-backdrop{
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 99;
    display: none;
}

.team-member-bio-modal{
    display: none;
    background: var(--color-light-teal);
    position: fixed;
    top: 0;
    right: 0;
    width: 68rem;
    max-width: 100%;
    height: 100%;
    z-index: 100;
    overflow: auto;
    overscroll-behavior: contain;
}

.team-member-bio-modal-inner{
    padding: 6rem var(--container-padding);
    width: 66rem;
    max-width: 100vw;
}

.team-member-bio-close{
    display: block;
    margin: 0 0 0.5rem auto;
    border: none;
    border-radius: 0;
    padding: 0;
    appearance: none;
    background: transparent;
    cursor: pointer;
}

.team-member-bio-close svg>*{
    transition: all 0.3s ease;
}

.team-member-bio-close:is(:hover, :focus, :active){
    outline: none;
}

.team-member-bio-close:is(:hover, :focus, :active) svg path{
    fill: var(--color-grey);
}

.team-member-bio-close:is(:hover, :focus, :active) svg rect{
    stroke: var(--color-grey);
}

.team-member-bio-info h3{
    font: var(--font-headline-4-semibold);
    letter-spacing: var(--font-headline-4--spacing);
}

.team-member-bio-info p{
    font: var(--font-body-large-regular);
    letter-spacing: var(--font-body-large--spacing);
}

.team-member-bio-info p a{
    color: var(--color-blue);
    text-decoration: none;
}

.team-member-bio-info p a:is(:hover, :focus, :active){
    outline: none;
    text-decoration: underline;
}

.team-member-bio-content{
    margin-top: 4rem;
    color: var(--color-black);
}

.team-member-bio-content p{
    margin-bottom: 2rem;
}

@media (min-height: 768px){
    .team-member-bio-modal-inner{
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .team-member-bio-content{
        overflow: auto;
        flex-shrink: 1;
    }
}

@media (min-width: 768px){
    .team-members-filter select, .team-members-filter .team-members-search-wrapper{
        width: auto;
    }

    .team-members-inner{
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .team-member-bio-modal{
        height: 80rem;
        max-height: 90vh;
        top: 50%;
        transform: translateY(-50%);
        border-radius: 1rem 0 0 1rem;
    }

    .team-member-bio-modal-inner{
        padding: 4rem;
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .team-member-bio-info{
        display: grid;
        grid-template-columns: repeat(2, minmax(0, 1fr));
        column-gap: 4rem;
        align-items: center;
    }

    .team-member-bio-info .team-member-full-card-image{
        margin-bottom: 0;
    }
}

@media (min-width: 992px){
    .team-members-inner{
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}

@media (min-width: 1200px){
    .team-members-inner{
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }
}
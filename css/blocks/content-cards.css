.content-cards{
    margin: var(--section-spacing) 0;
}

.content-cards-inner{
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    justify-content: space-between;
}

.content-cards-inner.animated{
    animation-name: fadeIn;
}

.content-cards-inner h2{
    font: var(--font-headline-4-semibold);
    letter-spacing: var(--font-headline-4--spacing);
    margin-bottom: 0;
}

.content-cards-inner h2 .subtitle{
    margin-bottom: 1.8rem;
}

.content-cards-posts{
    width: 100%;
    margin: 3rem 0;
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    column-gap: 2.4rem;
    row-gap: 3rem;
}

.post-card:not(a):is(:hover,:focus,:active) .post-card-thumbnail {
    border-bottom: .5rem solid var(--color-yellow);
}

.post-card:not(a):is(:hover,:focus,:active) h3 {
    color: var(--color-grey);
}

.content-cards-posts :not(a).post-card:is(:hover,:focus,:active) .post-card-thumbnail img {
    transform: none;
}

@media (min-width: 992px){
    .content-cards-inner .button-light-filled{
        order: 2;
        margin-left: auto;
    }

    .content-cards-posts{
        order: 3;
        margin-bottom: 0;
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .content-cards-posts[data-posts="2"] .post-card:first-child{
        grid-column: span 2;
    }
}
.posts-section{
    margin: var(--section-spacing) 0;
}

.posts-section header{
    border-bottom: 0.1rem dotted var(--color-warm-grey);
    padding-bottom: 1.5rem;
}

.posts-section header.animated{
    animation-name: fadeIn;
}

.posts-section header h2{
    font: var(--font-headline-3-semibold);
    letter-spacing: var(--font-headline-3--spacing);
    margin-bottom: 1rem;
}

.posts-section header .button-light-outline:after{
    transform: rotateZ(90deg) translateX(-0.1rem);
    transition: all 0.3s ease;
}

.posts-section.filters-open header .button-light-outline:after{
    transform: rotateZ(-90deg) translateX(0.1rem);
}

.posts-section .posts-filters{
    display: none;
    border-bottom: 0.1rem dotted var(--color-warm-grey);
    padding: 1.5rem 0;
}

.posts-section .posts-filters-inner{
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    column-gap: 3rem;
    row-gap: 3rem;
}

.posts-section .posts-filters-inner h3{
    font: var(--font-subtitle-semibold);
    letter-spacing: var(--font-subtitle--spacing);
    text-transform: uppercase;
    margin-bottom: 2rem;
}

.posts-section .posts-filters-inner ul{
    margin: 0;
    padding: 0;
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    column-gap: 2rem;
    row-gap: 2rem;
}

.posts-section .posts-filters-inner ul li{
    list-style: none;
    display: block;
}

.posts-section .posts-filters-inner ul li a{
    font: var(--font-body-regular-semibold);
    letter-spacing: var(--font-body-regular-semibold);
    color: var(--color-blue);
    text-decoration: none;
}

.posts-section .posts-filters-inner ul li a.active{
    color: var(--color-black);
    text-decoration: underline;
}

.posts-section .posts-section-container{
    margin-top: 1.5rem;
}

.posts-section .posts-section-container.animated{
    animation-name: fadeInUp;
}

.posts-section .posts-section-grid{
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    column-gap: 2.4rem;
    row-gap: 3rem;
}

.posts-section .pagination{
    text-align: center;
    margin-top: 3rem;
}

.posts-section .page-numbers{
    font: var(--font-button-semibold);
    letter-spacing: var(--font-button--spacing);
    text-transform: uppercase;
    text-decoration: none;
    border-radius: 1.2rem;
    padding: 1.5rem 1rem;
    min-width: 4rem;
    display: inline-block;
    transition: all 0.3s ease;
    cursor: pointer;
    color: var(--color-grey);
    background-color: var(--color-light-teal);
}

.posts-section .page-numbers:is(:hover, :focus, :active, .current){
    background-color: var(--color-yellow);
    text-decoration: none;
    outline: none;
}

.posts-section .page-numbers:focus{
    text-decoration: underline dashed;
    text-underline-offset: 0.2em;
}

.posts-section .post-card.disabled{
    pointer-events: none;
    cursor: auto;
}

.posts-section .project-body{
    margin-top: -2rem;
}


@media (min-width: 768px){
    .posts-section header{
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
    }

    .posts-section header h2{
        margin-bottom: 0;
    }

    .posts-section header a{
        margin-left: auto;
    }

    .posts-section .posts-filters-inner{
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

@media (min-width: 992px){
    .posts-section .posts-filters-inner ul{
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .posts-section .posts-section-grid{
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .posts-section .posts-section-grid .post-card:first-child{
        grid-column: span 2;
    }
}
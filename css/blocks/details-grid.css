.details-grid{
    margin: var(--section-spacing) 0;
}

.details-grid-inner{
    position: relative;
    padding: 6rem 0;
}

.details-grid-inner.animated{
    animation-name: fadeInLeft;
}

.details-grid-inner[data-background="blue"],
.details-grid-inner[data-background="blue"] h2{
    color: var(--color-white);
}

.details-grid-inner[data-background="blue"]:before{
    content: '';
    display: block;
    position: absolute;
    right: 10%;
    top: 0;
    width: 100vw;
    height: 100%;
    background-color: var(--color-blue);
    border-radius: 2rem;
}

.details-grid-inner header{
    position: relative;
    margin-bottom: 4rem;
}

.details-grid-inner[data-background="blue"] header{
    max-width: 90%;
    padding-right: var(--container-padding);
}

.details-grid-inner header h2{
    font: var(--font-headline-3-semibold);
    letter-spacing: var(--font-headline-3--spacing);
}

.details-grid-inner header p{
    font: var(--font-body-large-regular);
    letter-spacing: var(--font-body-large--spacing);
}

.details-grid-grid{
    position: relative;
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    row-gap: 6rem;
    column-gap: 6rem;
    background-color: var(--color-white);
    border-radius: 2rem;
    padding: 6rem 3rem;
    box-shadow: 0px 1.5rem 8rem rgba(0, 0, 0, 0.1);
    color: var(--color-grey);
}

.details-grid-item{
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.details-grid-grid[data-alignment="top"] .details-grid-item{
    justify-content: flex-start;
}

.details-grid-grid[data-alignment="center"] .details-grid-item{
    justify-content: center;
}

.details-grid-grid[data-alignment="bottom"] .details-grid-item{
    justify-content: flex-end;
}

.details-grid-item img{
    width: 8rem;
    height: 8rem!important;
    object-fit: contain;
    object-position: center;
    margin-bottom: 6rem;
}

.details-grid-item h3{
    font: var(--font-headline-5-semibold);
    letter-spacing: var(--font-headline-5--spacing);
    margin-bottom: 1.6rem;
}

.details-grid-item p{
    font: var(--font-body-small-regular);
    letter-spacing: var(--font-body-small--spacing);
}

.details-grid-item>*:first-child{
    margin-top: 0;
}

.details-grid-item>*:last-child{
    margin-bottom: 0;
}

@media (max-width: 767px){
    .details-grid-grid .details-grid-item:nth-child(n + 2):before{
        content: '';
        display: block;
        position: absolute;
        top: -3rem;
        width: 100%;
        left: 0;
        border-top: 0.1rem dotted var(--color-warm-grey);
    }

}

@media (min-width: 768px){
    .details-grid-inner[data-background="blue"]{
        color: var(--color-white);
    }

    .details-grid-inner[data-background="blue"]:before{
        right: 25%;
    }

    .details-grid-inner header,
    .details-grid-inner[data-background="blue"] header{
        max-width: 65%;
        padding-right: 0;
    }

    .details-grid-grid{
        padding: 6rem;
    }

    .details-grid-grid[data-items="2"]{
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .details-grid-grid[data-items="3"]{
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .details-grid-grid .details-grid-item{
        height: 100%;
    }

    .details-grid-grid[data-items="2"] .details-grid-item:nth-child(2n + 3):before,
    .details-grid-grid[data-items="3"] .details-grid-item:nth-child(3n + 4):before{
        content: '';
        display: block;
        position: absolute;
        top: -3rem;
        left: 0;
        border-top: 0.1rem dotted var(--color-warm-grey);
    }

    .details-grid-grid[data-items="2"] .details-grid-item:nth-child(2n + 3):before{
        width: calc(200% + 6rem);
    }

    .details-grid-grid[data-items="3"] .details-grid-item:nth-child(3n + 4):before{
        width: calc(300% + 12rem);
    }

    .details-grid-grid[data-items="2"] .details-grid-item:not(:nth-child(2n + 1)):before,
    .details-grid-grid[data-items="3"] .details-grid-item:not(:nth-child(3n + 1)):before{
        content: '';
        display: block;
        position: absolute;
        top: 0;
        left: -3rem;
        border-left: 0.1rem dotted var(--color-warm-grey);
        height: 100%;
    }

    .details-grid-item{
        padding: 2rem 0;
    }
}
.text-3-columns{
    margin: var(--section-spacing) auto;
}

.text-3-columns h2{
    font: var(--font-headline-3-semibold);
    letter-spacing: var(--font-headline-3--spacing);
    margin-bottom: 6rem;
}

.text-3-columns h2.animated{
    animation-name: fadeIn;
}

.text-3-columns-inner{
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    column-gap: 4rem;
    row-gap: 4rem;
}

.text-3-columns-column.animated{
    animation-name: fadeInUp;
}

.text-3-columns-inner h3{
    font: var(--font-headline-5-semibold);
    color: var(--color-blue);
    letter-spacing: var(--font-headline-5--spacing);
    margin-bottom: 1rem;
}

.text-3-columns-inner p{
    margin-bottom: 2rem;
}

.text-3-columns-inner a{
    font: var(--font-button-semibold);
    letter-spacing: var(--font-button--spacing);
    text-transform: uppercase;
}

.text-3-columns-inner>*:last-child{
    margin-bottom: 0;
}

@media (min-width: 768px){
    .text-3-columns-inner{
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}
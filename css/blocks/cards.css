.cards-section{
    margin: var(--section-spacing) 0;
}

.cards-section-inner{
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    column-gap: 2.4rem;
    row-gap: 2.4rem;
    counter-reset: card-count;
}

.cards-section .card{
    background-color: var(--color-light-teal);
    padding: 4rem;
    border-radius: 1.2rem;
}

.cards-section .card.animated{
    animation-name: fadeInUp;
}

.cards-section .card:before{
    counter-increment: card-count;
    content: "";
    display: block;
    font: var(--font-subtitle-semibold);
    letter-spacing: var(--font-subtitle--spacing);
    color: var(--color-navy);
    margin-bottom: 3rem;
}

.cards-section .card h2{
    font: var(--font-headline-4-semibold);
    letter-spacing: var(--font-headline-4--spacing);
}

.cards-section .card p{
    font: var(--font-body-large-regular);
    letter-spacing: var(--font-body-large--spacing);
}

.cards-section .card>*:last-child{
    margin-bottom: 0;
}

@media (min-width: 992px){
    .cards-section-inner{
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}
.primary-hero{
    overflow: hidden;
}

.primary-hero-container{
    padding: 28rem 0 0;
    position: relative;
}

.primary-hero-slider{
    border-radius: 1rem;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 30rem;
    z-index: 1;
}

.primary-hero-slider :is(img, video){
    width: 100%;
    height: 100%!important;
    object-fit: cover;
    object-position: center;
}

.primary-hero-inner{
    background-color: var(--color-blue);
    color: var(--color-white);
    padding: 3rem 1.5rem;
    max-width: 55rem;
    border-radius: 0 1rem 1rem 0;
    position: relative;
    z-index: 1;
}

.primary-hero-inner:before{
    content: '';
    display: block;
    background: var(--color-navy);
    height: 100%;
    width: 100vw;
    position: absolute;
    top: 0;
    right: 100%;
}

.primary-hero-inner h1{
    font: var(--font-headline-2-semibold);
    letter-spacing: var(--font-headline-2--spacing);
    color: var(--color-white);
}

.primary-hero-inner p{
    font: var(--font-body-large-regular);
    letter-spacing: var(--font-body-large--spacing);
    margin: 0;
}

.primary-hero-inner p a{
    color: var(--color-white);
    text-decoration-color: var(--color-white);
}

.primary-hero-inner p a:hover{
    color: var(--color-yellow);
    text-decoration-color: var(--color-yellow)
}

.primary-hero-inner h2 + p{
    margin-top: 3rem;
}

@media (min-width: 768px){
    .primary-hero-container{
        padding: 12.4rem 0;
    }

    .primary-hero-slider{
        height: 100%;
    }

    .primary-hero-inner{
        padding: 7.5rem 7rem 8.5rem 5.5rem;
    }
}
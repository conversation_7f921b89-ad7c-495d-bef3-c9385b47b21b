.tabs-section{
    margin: var(--section-spacing) 0;
}

.tabs-section-inner{
    position: relative;
    padding: 6rem 0;
}

.tabs-section-inner.animated{
    animation-name: fadeInLeft;
}

.tabs-section-inner[data-background="light-teal"]:before{
    content: '';
    display: block;
    position: absolute;
    right: 10%;
    top: 0;
    width: 100vw;
    height: 100%;
    background-color: var(--color-light-teal);
    border-radius: 2rem;
}

.tabs-section-inner header{
    position: relative;
    margin-bottom: 4rem;
}

.tabs-section-inner[data-background="light-teal"] header{
    max-width: 90%;
    padding-right: var(--container-padding);
}

.tabs-section-inner header h2{
    font: var(--font-headline-3-semibold);
    letter-spacing: var(--font-headline-3--spacing);
}

.tabs-section-inner[data-background="light-teal"],
.tabs-section-inner[data-background="light-teal"] h2{
    color: var(--color-blue);
}

.tabs-section-inner header h3{
    font: var(--font-headline-6-semibold);
    letter-spacing: var(--font-headline-6--spacing);
    margin-bottom: 1rem;
}

.tabs-section-inner header p{
    color: var(--color-grey);
}

.tabs-section-container{
    position: relative;
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    column-gap: 6rem;
    background-color: var(--color-white);
    border-radius: 2rem;
    box-shadow: 0 1.5rem 8rem rgba(0, 0, 0, 0.1);
    color: var(--color-grey);
}

.tabs-section-tab-selector{
    font: var(--font-headline-5-semibold);
    letter-spacing: var(--font-headline-5--spacing);
    color: var(--color-grey);
    appearance: none;
    display: block;
    border: none;
    border-radius: 0.5rem;
    border-bottom: 0.5rem solid transparent;
    padding: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    background: transparent;
    text-align: left;
}

.tabs-section-tab-selector:is(:hover, :focus, :active, .active){
    outline: none;
    color: var(--color-blue);
    border-color: var(--color-blue);
}

.tabs-section-container-content{
    position: relative;
    padding: var(--container-padding);
}

.tabs-section-container-item{
    display: none;
}

.tabs-section-container-item.active{
    display: block;
}

.tabs-section-container-item h3{
    font: var(--font-headline-4-semibold);
    letter-spacing: var(--font-headline-4--spacing);
    margin-bottom: 2rem;
}

.tabs-section-container-item a {
    color: var(--color-blue);
    text-decoration: none;
}

.tabs-section-container-item a:not(.button-light-outline):is(:hover, :focus, :active) {
    color: var(--color-navy);
    text-decoration: underline;
}

.tabs-section-container-item img{
    border-radius: 1rem;
    margin-bottom: 2rem;
}

.tabs-section-container-item>*:last-child{
    margin-bottom: 0;
}

@media (max-width: 767px){
    .tabs-section-container-tabs{
        display: flex;
        overflow: auto;
        padding: var(--container-padding);
    }

    .tabs-section-container-content{
        padding-bottom: calc(var(--container-padding) * 2);
    }

    .tabs-section-container-content:before{
        content: '';
        display: block;
        position: absolute;
        top: 0;
        left: var(--container-padding);
        width: calc(100% - var(--total-container-padding));
        border-top: 0.1rem dashed var(--color-warm-grey);
    }
}

@media (min-width: 768px){
    .tabs-section-inner[data-background="light-teal"]:before{
        right: 25%;
    }

    .tabs-section-inner header,
    .tabs-section-inner[data-background="light-teal"] header{
        max-width: 65%;
        padding-right: 0;
    }

    .tabs-section-container{
        grid-template-columns: minmax(0, 1fr) minmax(0, 2fr);
        padding: 3rem;
    }

    .tabs-section-tab-selector{
        border: none;
        border-left: 0.5rem solid transparent;
        padding-left: 4.5rem 3rem;
        width: 100%;
        margin-bottom: 1.5rem;
    }

    .tabs-section-container-content{
        padding-right: calc(var(--container-padding) * 2);
    }

    .tabs-section-container-content:before{
        content: '';
        display: block;
        position: absolute;
        top: 0;
        left: -3rem;
        height: 100%;
        border-right: 0.1rem dashed var(--color-warm-grey);
    }
}
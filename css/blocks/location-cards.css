.location-cards-section{
  margin: var(--section-spacing) 0;
}

.location-cards-section header h2{
  font: var(--font-headline-3-semibold);
  letter-spacing: var(--font-headline-3--spacing);
  margin-bottom: 2rem;
}

.location-cards-section header p{
  font: var(--font-body-large-regular);
  letter-spacing: var(--font-body-large--spacing);
}

.location-cards-section-inner{
  display: grid;
  grid-template-columns: minmax(0, 1fr);
  column-gap: 2.4rem;
  row-gap: 2.4rem;
}


.location-cards-section .location-card{
  background-color: var(--color-light-teal);
  padding: 4rem;
  border-radius: 1.2rem;
}

.location-cards-section .location-card[data-background="blue"]{
  background-color: var(--color-blue);
}

.location-cards-section .location-card.animated{
  animation-name: fadeInUp;
}

.location-cards-section .location-card h2{
  font: var(--font-headline-4-semibold);
  letter-spacing: var(--font-headline-4--spacing);
}

.location-cards-section .location-card[data-background="blue"] h2{
  color: var(--color-white);
}

.location-cards-section .location-card p{
  font: var(--font-body-large-regular);
  letter-spacing: var(--font-body-large--spacing);
}

.location-cards-section .location-card[data-background="blue"] :where(p, a){
  color: var(--color-white);
  text-decoration-color: var(--color-white);
}

.location-cards-section .location-card>*:last-child{
  margin-bottom: 0;
}

@media (min-width: 768px){
  .location-cards-section-inner{
      grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 1200px){
  .location-cards-section-inner{
      grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}
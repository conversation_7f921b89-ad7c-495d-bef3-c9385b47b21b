.page-navigation{
    background: var(--color-blue);
    color: var(--color-white);
}

.page-navigation-inner{
    display: flex;
    align-items: center;
    overflow: auto;
    padding: 2rem 0;
    column-gap: 2rem;
    white-space: nowrap;
}

.page-navigation-inner>*:first-child{
    padding-left: var(--container-padding);
    margin-left: auto;
}

.page-navigation-inner>*:last-child{
    padding-right: var(--container-padding);
    margin-right: auto;
}

.page-navigation-inner h2{
    font: var(--font-breadcrumbs-regular);
    letter-spacing: var(--font-breadcrumbs--spacing);
    text-transform: uppercase;
    margin: 0;
    color: var(--color-white);
}

.page-navigation-inner ul{
    padding: 0;
    margin: 0;
    display: flex;
    column-gap: 5.6rem;
}

.page-navigation-inner ul li{
    display: block;
    list-style: none;
}

.page-navigation-inner ul li a{
    color: var(--color-white);
    font: var(--font-body-large-regular);
    letter-spacing: var(--font-body-large--spacing);
    text-decoration: none;
}

.page-navigation-inner ul li a:is(:hover, :focus, :active){
    text-decoration: underline;
}

.primary-hero+.page-navigation{
    margin-top: 6rem;
}
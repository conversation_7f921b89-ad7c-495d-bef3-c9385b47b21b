.text-accordion-2-columns{
    margin: var(--section-spacing) 0;
}

.text-accordion-2-columns-intro h2{
    font: var(--font-headline-4-semibold);
    letter-spacing: var(--font-headline-4--spacing);
}

.text-accordion-2-columns-intro p{
    font: var(--font-body-large-regular);
    letter-spacing: var(--font-body-large--spacing);
    margin-bottom: 3rem;
}

.text-accordion-2-columns-intro a{
    margin-bottom: 3rem;
}

.accordion{
    border-bottom: 0.1rem dashed var(--color-teal);
    padding: 3rem 0;
}

.text-accordion-2-columns-content .accordion:first-child{
    padding-top: 0;
}

.accordion>button{
    border: none;
    border-radius: 0;
    background: transparent;
    cursor: pointer;
    padding: 0.5rem 4.6rem 0.5rem 0;
    appearance: none;
    display: block;
    font: var(--font-breadcrumbs-regular);
    letter-spacing: var(--font-breadcrumbs--spacing);
    text-transform: uppercase;
    color: var(--color-grey);
    width: 100%;
    text-align: left;
    position: relative;
    transition: all 0.3s ease;
}

.accordion.open>button,
.accordion>button:is(:hover, :focus, :active){
    color: var(--color-blue);
    outline: none;
}

.accordion>button:before,
.accordion>button:after{
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    right: 1.8rem;
    width: 1.6rem;
    border-top: 0.2rem solid var(--color-blue);
    transition: all 0.3s ease;
}

.accordion>button:after{
    transform: translate(50%, -50%) rotateZ(-90deg);
}

.accordion>button:before,
.accordion>button.open:after{
    transform: translate(50%, -50%);
}

.accordion>div{
    display: none;
}

.accordion>div>:last-child{
    margin-bottom: 0;
}

.accordion a {
    color: var(--color-blue);
    text-decoration: none;
}

.accordion a:is(:hover, :focus, :active) {
    color: var(--color-navy);
    text-decoration: underline;
}

@media (min-width: 768px){
    .text-accordion-2-columns-inner{
        display: grid;
        grid-template-columns: minmax(0, 3.52fr) minmax(0, 6fr);
        column-gap: 3rem;
    }
}
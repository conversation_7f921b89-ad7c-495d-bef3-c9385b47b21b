/*
Theme Name: Smythe
Author: Massive Media
Author URI: https://engagemassive.com/
Description:
Version: 1.0
Text Domain: mm-smy

*/


/* THEME */
:root{
    /* FONTS */
    --primary-font: 'Open Sans', sans-serif;
    --secondary-font: 'Montserrat', sans-serif;

    /* FONT STYLE */
    --font-headline-1--size: 4.8rem;
    --font-headline-1--line: 110%;
    --font-headline-1--spacing: -0.015em;
    --font-headline-1-semibold: 600 var(--font-headline-1--size)/var(--font-headline-1--line) var(--secondary-font);
    --font-headline-1-medium: 500 var(--font-headline-1--size)/var(--font-headline-1--line) var(--secondary-font);
    --font-headline-1-regular: 400 var(--font-headline-1--size)/var(--font-headline-1--line) var(--secondary-font);
    --font-headline-2--size: 3.6rem;
    --font-headline-2--line: 110%;
    --font-headline-2--spacing: -0.015em;
    --font-headline-2-semibold: 600 var(--font-headline-2--size)/var(--font-headline-2--line) var(--secondary-font);
    --font-headline-2-medium: 500 var(--font-headline-2--size)/var(--font-headline-2--line) var(--secondary-font);
    --font-headline-2-regular: 400 var(--font-headline-2--size)/var(--font-headline-2--line) var(--secondary-font);
    --font-headline-3--size: 3.0rem;
    --font-headline-3--line: 130%;
    --font-headline-3--spacing: 0;
    --font-headline-3-semibold: 600 var(--font-headline-3--size)/var(--font-headline-3--line) var(--secondary-font);
    --font-headline-3-medium: 500 var(--font-headline-3--size)/var(--font-headline-3--line) var(--secondary-font);
    --font-headline-3-regular: 400 var(--font-headline-3--size)/var(--font-headline-3--line) var(--secondary-font);
    --font-headline-4--size: 2.4rem;
    --font-headline-4--line: 130%;
    --font-headline-4--spacing: 0;
    --font-headline-4-semibold: 600 var(--font-headline-4--size)/var(--font-headline-4--line) var(--secondary-font);
    --font-headline-4-medium: 500 var(--font-headline-4--size)/var(--font-headline-4--line) var(--secondary-font);
    --font-headline-4-regular: 400 var(--font-headline-4--size)/var(--font-headline-4--line) var(--secondary-font);
    --font-headline-5--size: 2.0rem;
    --font-headline-5--line: 130%;
    --font-headline-5--spacing: 0;
    --font-headline-5-semibold: 600 var(--font-headline-5--size)/var(--font-headline-5--line) var(--secondary-font);
    --font-headline-5-medium: 500 var(--font-headline-5--size)/var(--font-headline-5--line) var(--secondary-font);
    --font-headline-5-regular: 400 var(--font-headline-5--size)/var(--font-headline-5--line) var(--secondary-font);
    --font-headline-6--size: 1.8rem;
    --font-headline-6--line: 130%;
    --font-headline-6--spacing: 0;
    --font-headline-6-semibold: 600 var(--font-headline-6--size)/var(--font-headline-6--line) var(--secondary-font);
    --font-headline-6-medium: 500 var(--font-headline-6--size)/var(--font-headline-6--line) var(--secondary-font);
    --font-headline-6-regular: 400 var(--font-headline-6--size)/var(--font-headline-6--line) var(--secondary-font);
    --font-body-large--size: 1.8rem;
    --font-body-large--line: 170%;
    --font-body-large--spacing: 0;
    --font-body-large-regular: 400 var(--font-body-large--size)/var(--font-body-large--line) var(--primary-font);
    --font-body-large-semibold: 600 var(--font-body-large--size)/var(--font-body-large--line) var(--primary-font);
    --font-body-regular--size: 1.5rem;
    --font-body-regular--line: 187%;
    --font-body-regular--spacing: 0.015em;
    --font-body-regular: 400 var(--font-body-regular--size)/var(--font-body-regular--line) var(--primary-font);
    --font-body-regular-semibold: 600 var(--font-body-regular--size)/var(--font-body-regular--line) var(--primary-font);
    --font-body-small--size: 1.4rem;
    --font-body-small--line: 160%;
    --font-body-small--spacing: 0;
    --font-body-small-regular: 400 var(--font-body-small--size)/var(--font-body-small--line) var(--primary-font);
    --font-body-small-semibold: 600 var(--font-body-small--size)/var(--font-body-small--line) var(--primary-font);
    --font-button--size: 1.3rem;
    --font-button--line: 160%;
    --font-button--spacing: 0;
    --font-button-semibold: 600 var(--font-button--size)/var(--font-button--line) var(--primary-font);
    --font-subtitle--size: 1.4rem;
    --font-subtitle--line: 175%;
    --font-subtitle--spacing: 0.15em;
    --font-subtitle-regular: 400 var(--font-subtitle--size)/var(--font-subtitle--line) var(--primary-font);
    --font-subtitle-medium: 500 var(--font-subtitle--size)/var(--font-subtitle--line) var(--primary-font);
    --font-subtitle-semibold: 600 var(--font-subtitle--size)/var(--font-subtitle--line) var(--primary-font);
    --font-breadcrumbs--size: 1.2rem;
    --font-breadcrumbs--line: 200%;
    --font-breadcrumbs--spacing: 0.15em;
    --font-breadcrumbs-regular: 400 var(--font-breadcrumbs--size)/var(--font-breadcrumbs--line) var(--primary-font);
    --font-breadcrumbs-semibold: 600 var(--font-breadcrumbs--size)/var(--font-breadcrumbs--line) var(--primary-font);
    --font-menu--size: 1.3rem;
    --font-menu--line: 185%;
    --font-menu--spacing: 0.05em;
    --font-menu: 600 var(--font-menu--size)/var(--font-menu--line) var(--secondary-font);


    /* COLORS */
    --color-blue: #00558C;
    --color-grey: #212322;
    --color-navy: #023B64;
    --color-teal: #419599;
    --color-yellow: #F4DA43;
    --color-warm-grey: #757870;
    --color-black: #000000;
    --color-white: #FFFFFF;

    --color-light-teal: #E3EFF0;
    --color-light-blue: #F5FAFC;


    /* HELPERS */
    --header-height: 10.2rem;
    --section-spacing: 6.5rem;
    --container-width: 100%;
    --half-container-width: calc(var(--container-width) / 2);
    --container-padding: 1.5rem;
    --total-container-padding: calc(var(--container-padding) * 2);
    --container-width-without-padding: calc(var(--container-width) - var(--total-container-padding));
    --admin-bar-height: 0rem;
}

html{
    font-size: 62.5%;
    min-height: 100%;
}

body{
    font: var(--font-body-regular);
    letter-spacing: var(--font-body-regular--spacing);
    color: var(--color-grey);
}

body.admin-bar{
    --admin-bar-height: var(--wp-admin--admin-bar--height);
}

body:not(.load-lazy-bg) .wait-lazy-bg{
    background-image: none!important;
}

a{
    color: var(--color-blue);
    transition: all 0.3s ease;
    text-decoration: none;
    text-decoration-color: var(--color-navy);
    text-underline-offset: 0.2em;
}

a:is(:hover, :focus, :active){
    color: var(--color-navy);
    text-decoration: underline;
}

.hidden,
.sr-only{
    display: none;
}

ol,
ul{
    padding-left: 2rem;
}

b,
strong{
    font-weight: 600;
}

p,
ol,
ul{
    margin-bottom: 3rem;
}

hr{
    border: none;
    border-top: 0.1rem solid var(--color-blue);
    opacity: 0.5;
}

.regular{
    font-weight: 400;
}

.medium{
    font-weight: 500;
}

.semibold{
    font-weight: 600;
}

h1,
h2,
h3,
h4,
h5{
    color: var(--color-navy);
    margin-bottom: 3rem;
}

h1,
.h1{
    font: var(--font-headline-1-semibold);
    letter-spacing: var(--font-headline-1--spacing);
}

h2,
.h2{
    font: var(--font-headline-2-semibold);
    letter-spacing: var(--font-headline-2--spacing);
}

h3,
.h3{
    font: var(--font-headline-3-semibold);
    letter-spacing: var(--font-headline-3--spacing);
}

h4,
.h4{
    font: var(--font-headline-4-semibold);
    letter-spacing: var(--font-headline-4--spacing);
}

h5,
.h5{
    font: var(--font-headline-5-semibold);
    letter-spacing: var(--font-headline-5--spacing);
}

h6,
.h6{
    font: var(--font-headline-6-semibold);
    letter-spacing: var(--font-headline-6--spacing);
}

.subtitle{
    font: var(--font-subtitle-semibold);
    letter-spacing: var(--font-subtitle--spacing);
    display: block;
    margin-bottom: 3rem;
    text-transform: uppercase;
}

.container{
    width: var(--container-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
}

html #wpadminbar{
    position: fixed;
    transition: all 0.3s ease;
}

.alignwide{
    margin-left: auto;
    margin-right: auto;
    padding: 0;
}

.blocks-container>*{
    --element-width: var(--container-width);
    max-width: calc(var(--element-width) - var(--total-container-padding));
    margin-left: auto!important;
    margin-right: auto!important;
}

.blocks-container>.alignwide,
.blocks-container>.alignfull{
    left: auto;
    transform: none;
}

.blocks-container>.alignwide{
    --element-width: var(--container-width);
}

.blocks-container>.alignfull{
    width: 100%;
    max-width: 100%;
}

@media (min-width: 768px){
    :root{
        /* FONTS */
        --font-headline-1--size: 9.7rem;
        --font-headline-2--size: 6.4rem;
        --font-headline-3--size: 4.8rem;
        --font-headline-4--size: 3.6rem;
        --font-headline-5--size: 2.4rem;
        --font-headline-6--size: 2.0rem;
        --font-body--size: 1.6rem;

        /* HELPERS */
        --container-width: 72rem;
        --section-spacing: 8rem;
    }
}

@media (min-width: 992px){
    :root{
        --container-width: 96rem;
    }

    .blocks-container>*{
        --element-width: 92.7rem;
    }
}

@media (min-width: 1200px){
    :root{
        --container-width: 117rem;
        --header-height: 11.6rem;
    }
}

@media (min-width: 1300px){
    :root{
        --container-width: 127.8rem;
    }
}


/* BUTTONS */
.button-light-filled,
.button-light-outline,
.wp-block-button .wp-block-button__link,
.wp-block-button.is-style-outline .wp-block-button__link{
    font: var(--font-button-semibold);
    letter-spacing: var(--font-button--spacing);
    text-transform: uppercase;
    text-decoration: none;
    border: none;
    border-radius: 1.2rem;
    padding: 1.5rem 3rem;
    appearance: none;
    display: inline-block;
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
}

.button-light-filled,
.wp-block-button .wp-block-button__link{
    color: var(--color-grey);
    background-color: var(--color-yellow);
}

.button-light-outline,
.wp-block-button.is-style-outline .wp-block-button__link{
    color: var(--color-grey);
    background-color: var(--color-light-teal);
}

.button-light-outline:after,
.wp-block-button.is-style-outline .wp-block-button__link:after{
    content: '';
    display: inline-block;
    width: 1.2rem;
    height: 1.2rem;
    background-image: url('img/button-arrow.svg');
    background-size: contain;
    background-repeat: no-repeat;
    margin-left: 1rem;
    vertical-align: middle;
}

.button-light-filled:is(:hover, :focus, :active),
.wp-block-button .wp-block-button__link.wp-block-button__link:is(:hover, :focus, :active){
    background-color: var(--color-grey);
    color: var(--color-yellow);
    text-decoration: none;
    outline: none;
}

.button-light-outline:is(:hover, :focus, :active),
.wp-block-button.is-style-outline .wp-block-button__link.wp-block-button__link:is(:hover, :focus, :active){
    background-color: var(--color-yellow);
    color: var(--color-grey);
    text-decoration: none;
}

:is(.button-light-filled, .button-light-outline):focus,
.wp-block-button.is-style-outline .wp-block-button__link:focus{
    text-decoration: underline dashed;
    text-underline-offset: 0.2em;
    outline: none;
}

/* single.php social share box */
.share-box.hide {
    display: none;
}  

/* HEADER */
.header{
    position: fixed;
    top: var(--admin-bar-height);
    left: 0;
    width: 100%;
    box-shadow: 0px 1.5rem 8rem rgba(0, 0, 0, 0.1);
    z-index: 100;
    transition: all 0.3s ease;
}

.fixed-navbar:not(.scrolling-up, .menu-open) .header{
    transform: translateY(-100%);
    box-shadow: none;
}

.topbar-header{
    color: var(--color-grey);
    padding: 1.5rem 0 0;
    background: var(--color-white);
}

.topbar-header .container {
    display: none;
}

.topbar-header .container .top-search-bar {
    display: grid;
    grid-column: 1;
    height: 3rem;
}

@keyframes search-bar {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

.topbar-header .container .top-search-bar input {
    display: none;
    width: 100%;
    border: 0.1rem solid var(--color-navy);
    padding: 0 1rem;
    border-radius: 0.8rem;
    animation: search-bar 0.3s ease-in-out;
}

.topbar-header .container .menu-topbar-menu-container {
    display: grid;
    grid-column: 2;
    align-items: center;
}

.topbar-header .menu{
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    column-gap: 3rem;
    line-height: 1;
}

.topbar-header .menu li{
    display: block;
    list-style: none;
}

.topbar-header .menu li a{
    font: var(--font-menu);
    letter-spacing: var(--font-menu--spacing);
    color: var(--color-grey);
    text-decoration: none;
    transition: 0.3s ease all;
}

.topbar-header .menu li a:is(:hover, :focus, :active){
    color: var(--color-navy);
    text-decoration: underline;
}

.top-menu-search a {
    width: fit-content;
    display: flex;
    align-items: center;
}

.topbar-header .menu li.top-menu-search a::before {
    display: flex;
    content: '';
    background-color: var(--color-grey);
    -webkit-mask-image: url(https://stg-smysmythe-staging.kinsta.cloud/wp-content/themes/mm-smythe/img/icon-search.svg);
    mask-image: url(https://stg-smysmythe-staging.kinsta.cloud/wp-content/themes/mm-smythe/img/icon-search.svg);
    width: 1.9rem;
    height: 2rem;
    flex-direction: column;
    margin-right: 1rem;
}

.topbar-header .menu li.top-menu-search a:is(:hover, :focus, :active)::before {
    background-color: var(--color-navy);
}

.main-header{
    background: var(--color-white);
}

.main-header-inner{
    display: flex;
    align-items: center;
}

.main-header .logo{
    padding: 1rem 0;
}

.main-header .logo img{
    width: 18.9rem;
}

.menu-widget{
    max-width: 38.2rem;
    text-align: left;
}

.menu-widget-card{
    position: relative;
    border-bottom: 0.5rem solid var(--color-teal);
    border-radius: 1rem;
    overflow: hidden;
    display: grid;
    align-items: center;
    margin-bottom: 1rem;
}

.menu-widget-background{
    position: relative;
    grid-column: 1;
    grid-row: 1;
}

.menu-widget-background img{
    width: 100%;
}

.menu-widget-background:after{
    content: '';
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--color-black);
    opacity: 0.4;
}

.menu-widget-card h2{
    font: var(--font-headline-5-semibold);
    letter-spacing: var(--font-headline-5--spacing);
    color: var(--color-white);
    grid-column: 1;
    grid-row: 1;
    position: relative;
    width: 100%;
    padding: 2rem;
    margin-bottom: 0;
}

.menu-widget a{
    font: var(--font-body-large-semibold);
    letter-spacing: var(--font-body-large--spacing);
    color: var(--color-blue);
    text-decoration: none;
}

.menu-widget a:after{
    content: '';
    display: inline-block;
    width: 2rem;
    height: 2rem;
    background-image: url('img/header-arrow.svg');
    background-size: contain;
    background-repeat: no-repeat;
    margin-left: 0.5rem;
    vertical-align: middle;
    transition: all 0.3s ease;
}

.menu-widget a:is(:hover, :focus, :active):after{
    margin-left: 1rem;
}

@media (max-width: 767px) {
    .mobile-search {
        display: block;
    }

    .mobile-search .topbar-header {
        padding: 0;
    }
    
    .mobile-search .topbar-header .container {
        display: block;
        padding: 0;
    }

    .mobile-search .topbar-header .menu {
        display: block;
    }

    .mobile-search .topbar-header .container .top-search-bar {
        height: auto;
        margin-bottom: 1rem;
    }

    .mobile-search .topbar-header .container .top-search-bar input {
        padding: 1rem;
    }

    .mobile-search .topbar-header .container .menu-topbar-menu-container .menu li,
    .mobile-search .topbar-header .container .menu-topbar-menu-container .menu li a {
        display: flex;
    }

    .hide-on-mobile {
        display: none;
    }
    
}

@media (max-width: 1199px){
    .main-header .toggle-menu{
        background: transparent;
        padding: 0;
        margin: 0 0 0 auto;
        border: none;
        border-radius: 0;
        appearance: none;
        cursor: pointer;
    }

    .main-header .toggle-menu .menu-bar{
        display: block;
        margin: 0.8rem 0;
        width: 3rem;
        height: 0.3rem;
        background: var(--color-black);
        transition: all 0.3s ease;
    }

    .main-header .toggle-menu:focus{
        outline: none;
    }

    .main-header .toggle-menu:focus .menu-bar{
        background: var(--color-blue);
    }

    .main-header .toggle-menu .menu-bar:nth-child(1){
        transform-origin: bottom right;
    }

    .menu-open .main-header .toggle-menu .menu-bar:nth-child(1){
        transform: rotateZ(-45deg);
    }

    .menu-open .main-header .toggle-menu .menu-bar:nth-child(2){
        opacity: 0;
    }

    .main-header .toggle-menu .menu-bar:nth-child(3){
        transform-origin: top right;
    }

    .menu-open .main-header .toggle-menu .menu-bar:nth-child(3){
        transform: rotateZ(45deg);
    }

    .main-header nav{
        opacity: 0;
        pointer-events: none;
        transition: all 0.3s ease;
        position: absolute;
        top: 100%;
        left: 0;
        height: calc(100vh - 100%);
        background: var(--color-white);
        width: 100%;
        overflow: auto;
        padding: 3rem 0 10rem;
        overscroll-behavior: contain;
    }

    .menu-open .main-header nav{
        opacity: 1;
        pointer-events: all;
    }

    .main-header nav>div{
        width: var(--container-width);
        padding: 0 var(--container-padding);
        margin: 0 auto;
    }

    .main-header nav .menu{
        padding: 0;
        margin: 0;
    }

    .main-header nav .menu>li{
        display: block;
        margin-bottom: 3.6rem;
        position: relative;
    }

    .main-header nav .menu>li:not(:last-child):after{
        content: '';
        display: block;
        position: absolute;
        bottom: -1.8rem;
        left: 0;
        border-bottom: 0.1rem solid var(--color-grey);
        opacity: 0.2;
    }

    .main-header nav .menu>li>a{
        font: var(--font-menu);
        letter-spacing: var(--font-menu--spacing);
        text-decoration: none;
        display: block;
    }

    .main-header nav .sub-menu-toggle{
        position: absolute;
        top: 0;
        right: 0;
        width: 2.4rem;
        height: 2.4rem;
        padding: 0;
        margin: 0;
        border: none;
        border-radius: 0;
        appearance: none;
        background: transparent;
    }

    .main-header nav .sub-menu-toggle:focus{
        outline: none;
    }

    .main-header nav .sub-menu-toggle:after{
        content: '';
        display: block;
        position: absolute;
        top: 0.6rem;
        left: 0.6rem;
        width: 1.2rem;
        height: 1.2rem;
        border-color: var(--color-black);
        border-top: 0.2rem solid;
        border-right: 0.2rem solid;
        transform: translate(0, -25%) rotateZ(135deg);
        transition: all 0.3s ease;
    }

    .main-header nav .sub-menu-toggle:focus:after{
        border-color: var(--color-blue);
    }

    .main-header nav li.sub-menu-open>.sub-menu-toggle:after{
        transform: translate(0, 25%) rotateZ(-45deg);
    }

    .main-header nav .menu>li>.sub-menu{
        display: none;
        padding: 0.1rem 0;
        margin: 0;
    }

    .main-header nav .menu>li>.sub-menu>li{
        display: block;
        list-style: none;
        position: relative;
        margin: 1.8rem 0 1.8rem 1.8rem;
    }

    .main-header nav .menu>li>.sub-menu>li>a{
        font: var(--font-menu);
        letter-spacing: var(--font-body-large--spacing);
        color: var(--color-blue);
        text-decoration: none;
    }

    .main-header nav .menu>li>.sub-menu>li>a:is(:hover, :focus){
        text-decoration: underline;
    }

    .main-header nav .menu>li>.sub-menu>li>.sub-menu{
        display: none;
        padding: 0.1rem 0;
        margin: 0;
    }

    .main-header nav .menu>li>.sub-menu>li>.sub-menu>li{
        display: block;
        list-style: none;
        position: relative;
        margin: 1.8rem 0 1.8rem 1.8rem;
    }

    .main-header nav .menu>li>.sub-menu>li>.sub-menu>li>a{
        font: var(--font-menu);
        letter-spacing: var(--font-body-large--spacing);
        color: var(--color-blue);
        text-decoration: none;
    }

    .main-header nav .menu>li .sub-menu-toggle{
        top: 0.3rem;
    }

    .main-header nav .menu>li>.sub-menu>li>.sub-menu{
        display: none;
    }

    .main-header nav .menu>li.submenu-widget>.sub-menu>li>.sub-menu{
        display: block;
    }

    .main-header nav .menu>li.submenu-widget>.sub-menu>li>a[href="#"],
    .main-header nav .menu>li.submenu-widget>.sub-menu>li>.sub-menu-toggle{
        display: none;
    }

    .main-header nav .menu>li.submenu-widget>.sub-menu>li>a[href="#"] ~ .sub-menu{
        padding: 0;
    }

    .main-header nav .menu>li.submenu-widget>.sub-menu>li>a[href="#"] ~ .sub-menu>li{
        margin-left: 0;
    }
}

@media (min-width: 1200px){
    .main-header .toggle-menu{
        display: none;
    }

    .main-header nav{
        flex-grow: 1;
    }

    .main-header nav .menu{
        padding: 0;
        margin: 0;
        display: flex;
        align-items: center;
    }

    .main-header nav .menu>li{
        display: block;
    }

    .main-header nav .menu>li>a{
        font: var(--font-menu);
        letter-spacing: var(--font-menu--spacing);
        text-decoration: none;
        display: block;
        padding: 3rem 1.5rem;
    }

    .main-header nav .menu>li:is(:hover, :focus, :active, :focus-within)>a {
        text-decoration: underline;
        outline: none;
    }

    .main-header nav .menu>li.disabled>a{
        pointer-events: none;
        cursor: auto;
    }

    .main-header nav .menu>li>.sub-menu-toggle,
    .main-header nav .menu>li>ul>li>.sub-menu-toggle{
        display: none;
    }

    .main-header nav .menu>li:first-child,
    .main-header nav .menu>li:not(.menu-item-has-children):last-child{
        margin-left: auto;
    }

    .main-header nav .menu > li:is(.menu-item-has-children) > a::after {
        content: '';
        width: 0.5rem;
        height: 0.5rem;
        border: 1px solid var(--color-black);
        border-width: 0px 2px 2px 0;
        display: inline-block;
        padding: 3px;
        translate: 0px -2px;
        transform: rotate(45deg);
        margin: 0 1.2rem;
    }

    .main-header nav .menu>li.menu-item-has-children:last-child{
        margin-right: auto;
    }

    .main-header nav .menu>li:not(.menu-item-has-children):last-child>a{
        font: var(--font-button-semibold);
        letter-spacing: var(--font-button--spacing);
        text-transform: uppercase;
        text-decoration: none;
        border: none;
        border-radius: 1.2rem;
        padding: 1.5rem 3rem;
        appearance: none;
        display: inline-block;
        transition: all 0.6s ease;
        position: relative;
        cursor: pointer;
        color: var(--color-grey);
        background-color: var(--color-yellow);
    }

    .main-header nav .menu>li:is(:hover, :focus, :active, :focus-within):not(.menu-item-has-children):last-child>a{
        background-color: var(--color-grey);
        color: var(--color-yellow);
        text-decoration: none;
        outline: none;
    }

    .main-header nav .menu>li.menu-item-has-children>.sub-menu{
        opacity: 0;
        pointer-events: none;
        transition: all 0.6s ease;
        position: absolute;
        top: 100%;
        background-color: var(--color-white);
        box-shadow: 0px 8rem 8rem rgba(0, 0, 0, 0.1);
        padding: calc(var(--container-padding) * 2);
        margin: 0;
        border-radius: 0 0 1rem 1rem;
    }

    .main-header nav .menu>li.menu-item-has-children:is(:hover, :focus, :focus-within)>.sub-menu{
        opacity: 1;
        pointer-events: all;
    }

    .main-header nav .menu > li.menu-item-has-children > .sub-menu > li {
        display: block!important;
        list-style: none;
        margin-bottom: 1.6rem;
    }

    .main-header nav .menu>li.menu-item-has-children>.sub-menu>li:last-child {
        margin-bottom: 0;
    }

    .main-header nav .menu>li.menu-item-has-children>.sub-menu>li>a{
        font: var(--font-menu);
        letter-spacing: var(--font-body-large--spacing);
        color: var(--color-blue);
        text-decoration: none;
    }

    .main-header nav .menu>li.menu-item-has-children>.sub-menu>li>a:is(:hover, :focus, :active){
        text-decoration: underline;
    }

    .main-header nav .menu>li.menu-item-has-children>.sub-menu>li.menu-item-has-children>a[href="#"]{
        pointer-events: none;
    }

    .main-header nav .menu>li.menu-item-has-children>.sub-menu>li.menu-item-has-children>.sub-menu{
        display: block!important;
        margin: 0;
        padding: 0;
    }

    .main-header nav .menu>li.menu-item-has-children>.sub-menu>li.menu-item-has-children>.sub-menu>li{
        display: block!important;
        list-style: none;
        text-indent: 2rem;
    }

    .main-header nav .menu>li.menu-item-has-children>.sub-menu>li.menu-item-has-children>.sub-menu>li:is(:last-child) {
        margin-bottom: 1.6rem;
    }

    .main-header nav .menu>li.menu-item-has-children>.sub-menu>li.menu-item-has-children>.sub-menu>li:not(:last-child){
        margin-bottom: 0;
    }

    .main-header nav .menu>li.menu-item-has-children>.sub-menu>li.menu-item-has-children>.sub-menu>li>a{
        font: var(--font-body-large-regular);
        letter-spacing: var(--font-body-large--spacing);
        color: var(--color-blue);
        text-decoration: none;
    }

    .main-header nav .menu>li.menu-item-has-children>.sub-menu>li.menu-item-has-children>.sub-menu>li>a:is(:hover, :focus, :active){
        text-decoration: underline;
        outline: none;
    }

    .main-header nav .menu>li.menu-item-has-children.submenu-4cols>.sub-menu>li:not(:last-child){
        position: relative;
    }

    .main-header nav .menu>li.menu-item-has-children.submenu-4cols>.sub-menu>li:not(:last-child):after{
        content: '';
        display: block;
        position: absolute;
        top: 0;
        height: 100%;
        margin-left: 3rem;
        opacity: 0.2;
    }

    .main-header nav .menu>li.menu-item-has-children.submenu-3cols>.sub-menu>li{
        margin-bottom: 1.6rem;
    }

    .main-header nav .menu>li.menu-item-has-children.submenu-3cols>.sub-menu>li>a{
        line-height: 173%;
    }

    .main-header nav .menu>li.menu-item-has-children.submenu-widget>.sub-menu>.menu-item-widget{
        position: relative;
        grid-column: 2;
        grid-row: 1 / 100;
    }

    .main-header nav .menu>li.menu-item-has-children.submenu-widget>.sub-menu>.menu-item-widget:after{
        content: '';
        display: block;
        position: absolute;
        right: 100%;
        top: 0;
        height: 100%;
        margin-right: 2.9rem;
        border-right: 0.1rem solid var(--color-warm-grey);
        opacity: 0.2;
    }

    .main-header nav .menu>li.menu-item-has-children:not(.submenu-4cols, .submenu-widget)>.sub-menu>li>.sub-menu{
        display: none!important;
    }
}


/* FLOATING CTA */
.floating-cta{
    position: fixed;
    top: 50%;
    right: 0;
    z-index: 50;
    font: var(--font-button-semibold);
    letter-spacing: var(--font-button--spacing);
    text-transform: uppercase;
    text-decoration: none;
    border-radius: 4rem 4rem 0 0;
    padding: 2.4rem 4.4rem;
    transition: all 0.3s ease;
    cursor: pointer;
    color: var(--color-grey);
    background-color: var(--color-yellow);
    transform: translateY(-50%) rotateZ(-90deg) translate(50%, -50%);
    transform-origin: center right;
}

.floating-cta:is(:hover, :focus, :active){
    background-color: var(--color-grey);
    color: var(--color-yellow);
    text-decoration: none;
    outline: none;
}


/* PAGE - DEFAULT TEMPLATE */
.default-template{
    margin: calc(var(--header-height) + (var(--section-spacing) / 2)) 0 var(--section-spacing);
    transition: all 0.1s ease;
}

.default-template .blocks-container>h1{
    font: var(--font-headline-3-semibold);
    letter-spacing: var(--font-headline-3--spacing);
}

.default-template .blocks-container>h2{
    font: var(--font-headline-4-semibold);
    letter-spacing: var(--font-headline-4--spacing);
}

.default-template .blocks-container>h3{
    font: var(--font-headline-5-semibold);
    letter-spacing: var(--font-headline-5--spacing);
}

.default-template .blocks-container>:is(h3, h4, h5){
    font: var(--font-headline-6-semibold);
    letter-spacing: var(--font-headline-6--spacing);
}

.default-template .blocks-container>blockquote{
    padding: 0;
    margin-bottom: 3rem;
}

.default-template .blocks-container>blockquote p{
    font: var(--font-headline-4-medium);
    letter-spacing: var(--font-headline-4--spacing);
    color: var(--color-navy);
    margin-bottom: 1rem;
}

.default-template .blocks-container>blockquote cite{
    font-style: normal;
    color: var(--color-grey);
}

.default-template .blocks-container>blockquote cite strong{
    font-weight: 600;
}

.default-template .blocks-container>blockquote>*:last-child{
    margin-bottom: 0;
}

.default-template .blocks-container>.wp-block-separator{
    border: none;
    border-top: 0.1rem solid var(--color-blue);
    opacity: 0.5;
}


/* POST - DEFAULT TEMPLATE */
.post-template>.blocks-container>h1{
    font: var(--font-headline-2-semibold);
    letter-spacing: var(--font-headline-2--spacing);
}

.post-template .breadcrumbs{
    display: flex;
    flex-wrap: wrap;
    margin: 0 0 4.5rem;
    padding: 0;
}

.post-template .breadcrumbs li{
    list-style: none;
    display: block;
    font: var(--font-breadcrumbs-regular);
    letter-spacing: var(--font-breadcrumbs--spacing);
    text-transform: uppercase;
}

.post-template .breadcrumbs li:not(:last-child){
    padding-right: 3.6rem;
    position: relative;
}

.post-template .breadcrumbs li:not(:last-child):after{
    content: '';
    display: block;
    width: 0.5rem;
    height: 0.5rem;
    border-left: 0.1rem solid var(--color-blue);
    border-top: 0.1rem solid var(--color-blue);
    position: absolute;
    top: 50%;
    right: 1.5rem;
    transform: translateY(-50%) rotateZ(135deg);
}

.post-template .breadcrumbs li a{
    font: var(--font-breadcrumbs-semibold);
    color: var(--color-blue);
    text-decoration: none;
}

.post-template .breadcrumbs li a:is(:hover, :focus, :active){
    color: var(--color-navy);
    text-decoration: underline;
    text-decoration-color: var(--color-navy);
}

.post-template .post-date{
    display: block;
    font: var(--font-breadcrumbs-semibold);
    letter-spacing: var(--font-breadcrumbs--spacing);
    margin-bottom: 6rem;
    text-transform: uppercase;
}

.post-template .main-post-image{
    margin-bottom: 6rem;
}

.post-template .main-post-image img{
    width: 100%;
    height: 30rem!important;
    object-fit: cover;
    object-position: center;
}

.share-box,
.tags-list{
    display: flex;
    align-items: center;
    margin-bottom: var(--section-spacing);
}

.tags-list{
    align-items: flex-end;
    margin-top: calc((var(--section-spacing) * -1) + 4rem);
}

.share-box h2,
.tags-list h2{
    font: var(--font-breadcrumbs-semibold);
    letter-spacing: var(--font-breadcrumbs--spacing);
    color: var(--color-grey);
    text-transform: uppercase;
    margin: 0 4rem 0 0;
}

.share-box ul,
.tags-list ul{
    padding: 0;
    margin: 0;
}

.share-box ul{
    column-gap: 2rem;
    display: flex;
    flex-wrap: wrap;
}

.share-box ul li{
    display: block;
    list-style: none;
}

.tags-list ul li{
    display: inline;
    list-style: none;
}

.share-box ul li a{
    display: block;
}

.share-box ul li a svg{
    width: 2rem;
    display: block;
}

.share-box ul li a svg path{
    transition: all 0.3s ease;
}

.share-box ul li a:is(:hover, :focus, :active) svg path{
    fill: var(--color-grey);
}

.tags-list ul li a{
    color: var(--color-blue);
    text-decoration-color: var(--color-blue);
}

.tags-list ul li a:is(:hover, :focus, :active){
    color: var(--color-grey);
    text-decoration-color: var(--color-grey);
}

.tags-list ul li:not(:last-child):after{
    content: ',';
}

.share-box ~ hr{
    width: 100%;
}

.share-box ~ hr:last-child{
    display: none;
}

@media (min-width: 768px){
    .mobile-search {
        display: none;
    }

    .topbar-header .container {
        display: grid;
        grid-template-columns: 50% 50%;
    }

    .post-template .main-post-image img{
        height: 60rem!important;
    }
}


/* POST - TEAM MEMBERS */
.team-member-template .breadcrumbs{
    display: flex;
    flex-wrap: wrap;
    margin: 0 0 4.5rem;
    padding: 0;
}

.team-member-template .breadcrumbs li{
    list-style: none;
    display: block;
    font: var(--font-breadcrumbs-regular);
    letter-spacing: var(--font-breadcrumbs--spacing);
    text-transform: uppercase;
}

.team-member-template .breadcrumbs li:not(:last-child){
    padding-right: 3.6rem;
    position: relative;
}

.team-member-template .breadcrumbs li:not(:last-child):after{
    content: '';
    display: block;
    width: 0.5rem;
    height: 0.5rem;
    border-left: 0.1rem solid var(--color-blue);
    border-top: 0.1rem solid var(--color-blue);
    position: absolute;
    top: 50%;
    right: 1.5rem;
    transform: translateY(-50%) rotateZ(135deg);
}

.team-member-template .breadcrumbs li a{
    font: var(--font-breadcrumbs-semibold);
    color: var(--color-blue);
    text-decoration: none;
}

.team-member-template .breadcrumbs li a:is(:hover, :focus, :active){
    color: var(--color-navy);
    text-decoration: underline;
}

.team-member-inner{
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    column-gap: 8rem;
    row-gap: 6rem;
}

.team-member-inner-image{
    overflow: hidden;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    position: relative;
}

.team-member-inner-image::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80%;
    height: 2rem;
    background-color: var(--color-yellow);
}

.team-member-inner-image img{
    width: 100%;
    aspect-ratio: 292 / 368;
    object-fit: cover;
    object-position: center top;
}

.team-member-inner-info p a {
    color: var(--color-blue);
    text-decoration: none;
    font: var(--font-menu);
    letter-spacing: var(--font-menu--spacing);
    text-transform: uppercase;
}

.team-member-inner-info p a:is(:hover, :focus, :active) {
    color: var(--color-navy);
    text-decoration: underline;
}

.team-member-cta h2 {
    font: var(--font-headline-3-semibold);
}

.team-member-cta p {
    font: var(--font-body-large-regular);
}

.team-member-cta .button-light-filled {
    background-color: var(--color-navy);
    color: var(--color-white);
    text-transform: none;
    border-radius: 9999px;
    font: var(--font-button--size);
    font-weight: 600;
}

.team-member-cta .button-light-filled:is(:hover,:focus,:active) {
    background-color: var(--color-yellow);
    color: var(--color-grey);
}

.team-member-inner-position{
    font: var(--font-headline-4-semibold);
    color: var(--color-blue);
    margin-bottom: 0;
}

.team-member-inner-experience,
.team-member-inner-service{
    font: var(--font-subtitle-semibold);
    letter-spacing: var(--font-subtitle--spacing);
    font-size: 1.3rem;
    text-transform: uppercase;
    color: var(--color-blue);
}

.team-member-inner-info .social-media{
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
}

.team-member-inner-disclaimer{
    font: var(--font-body-small-regular);
    color: var(--color-blue);
}

:is(.team-member-inner-info, .team-member-inner-bio) h1{
    font: var(--font-headline-2-semibold);
    letter-spacing: var(--font-headline-2--spacing);
    color: var(--color-blue);
}

.team-member-inner-contact-row {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
    margin-top: 3rem;
    margin-bottom: 2rem;
}

.team-member-inner-contact-row .team-member-inner-location {
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    margin: 0;
    text-transform: uppercase;
}

.team-member-inner-location svg {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    flex-shrink: 0;
}

.team-member-inner-location svg path {
    fill: var(--color-blue);
}

.team-member-inner-location svg circle {
    fill: var(--color-white);
    fill-opacity: 1;
}

.team-member-inner-contact-row .social-media {
    display: flex;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.team-member-inner-contact-row .social-media li {
    margin-bottom: 0;
}

.team-member-inner-contact-row .social-media li a {
    text-transform: none;
    display: flex;
    align-items: center;
    gap: 1rem;
    border: 1px solid currentColor;
    border-radius: 9999px;
    padding: 0.8rem 1.6rem;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.team-member-inner-contact-row .social-media li svg path {
    transition: fill 0.2s ease;
}

.team-member-inner-contact-row .social-media li a:hover {
    background-color: var(--color-navy);
    color: var(--color-white);
}

.team-member-inner-contact-row .social-media li a:hover svg path {
    fill: var(--color-white);
}

.team-member-intro {
    font: var(--font-body-large-regular);
}

.team-member-inner-bio, .team-member-cta {
   color: var(--color-blue); 
}

.team-member-inner-bio h1{
    display: none;
    margin-bottom: 0;
}

.team-member-inner-bio a {
    color: var(--color-blue);
    text-decoration: none;
}

.team-member-inner-bio a:is(:hover, :focus, :active) {
    color: var(--color-navy);
    text-decoration: underline;
}

.team-member-insights, .team-member-speaking {
    margin-top: 4rem;
}

:is(.team-member-insights, .team-member-speaking) h3 {
    font: var(--font-headline-4-semibold);
}

.team-member-speaking ul li p {
    margin-bottom: 0;
}

.team-member-insights ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.team-member-insights ul li a {
    font-weight: 600;
    text-decoration: none;
}


@media (min-width: 768px){
    .team-member-inner{
        grid-template-columns: 33.8rem minmax(0, auto);
    }

    .team-member-inner-info h1{
        display: none;
    }

    .team-member-inner-bio h1{
        display: block;
    }
}


/* PAGE - EMPTY CANVAS */
.home .empty-canvas {
    margin-top: calc(var(--header-height) - 3rem);
}

.empty-canvas{
    margin-top: var(--header-height);
    transition: all 0.1s ease;
}


/* PAGE - ARCHIVE SECTION */
.archive-section{
    margin-top: var(--header-height);
    transition: all 0.1s ease;
}


/* PAGE - MORE SINGLE */
.single-more .post-template > .blocks-container > h1:not(:has(+time)){
    margin-bottom: 6rem;
}

@media (min-width: 992px){
    .single-more .blocks-container > *{
        --element-width: var(--container-width);
    }
}


/* POST CARD */
.post-card,
.post-card:is(:hover, :focus, :active){
    text-decoration: none;
}

.post-card .post-card-thumbnail{
    height: 30.1rem;
    position: relative;
    border-bottom: 0.5rem solid var(--color-yellow);
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
    margin-bottom: 3rem;
    background-color: var(--color-light-teal);
}

.post-card:is(:hover, :focus, :active) .post-card-thumbnail{
    border-bottom-color: var(--color-teal);
}

.post-card .post-card-thumbnail img{
    width: 100%;
    height: 100%!important;
    object-fit: cover;
    object-position: center;
    transition: all 0.3s ease;
}

.post-card:is(:hover, :focus, :active) .post-card-thumbnail img{
    transform: scale(1.1);
    transition: all 5s linear;
}

.post-card .post-card-meta{
    display: flex;
    margin-bottom: 1rem;
}

.post-card .post-card-meta .content-type{
    font: var(--font-breadcrumbs-regular);
    letter-spacing: var(--font-breadcrumbs--spacing);
    text-transform: uppercase;
}

.post-card .post-card-meta time{
    font: var(--font-breadcrumbs-semibold);
    letter-spacing: var(--font-breadcrumbs--spacing);
    text-transform: uppercase;
    margin-left: auto;
    padding-left: 0;
    flex-shrink: 0;
}

.post-card h3{
    font: var(--font-headline-5-semibold);
    letter-spacing: var(--font-headline-5--spacing);
    color: var(--color-grey);
    transition: all 0.3s ease;
    max-width: 90%;
}

.post-card:is(:hover, :focus, :active) h3{
    color: var(--color-blue);
}


/* 404 PAGE */
.page-not-found h1{
    color: var(--color-blue);
}


/* FOOTER */
.footer{
    position: relative;
    z-index: 10;
    background-color: var(--color-navy);
    color: var(--color-white);
    padding: 6rem 0;
}

.footer.bottom-space-small{
    padding: 6rem 0;
}

.footer.bottom-space-medium{
    padding: 6rem 0 16rem;
}

.footer.bottom-space-large{
    padding: 6rem 0 20rem;
}

.footer a{
    color: var(--color-white);
}

.footer-1{
    padding-bottom: 4rem;
    margin-bottom: 4rem;
    border-bottom: 0.1rem solid var(--color-white);
}

.footer-1[data-grid-type='two-rows-three-columns'] {
    margin-bottom: unset;
    border-bottom: unset;
}

.footer-2{
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    column-gap: 6rem;
    row-gap: 6rem;
}

.footer-2[data-grid-type='two-rows-three-columns'] {
    row-gap: 3rem;
}

.footer-2[data-grid-type='two-rows-three-columns'] h6 {
    margin-bottom: 2rem;
    font-size: var(--font-body-regular--size);
    font-weight: 800;
}

.footer-2[data-grid-type='two-rows-three-columns'] .wp-block-columns {
    flex-wrap: nowrap !important;
}

.footer-2[data-grid-type='two-rows-three-columns'] .footer-widgets:nth-of-type(4) {
    padding-top: 3.25rem;
    border-top: solid 1px var(--color-white);
}

.footer-2[data-grid-type='two-rows-three-columns'] .footer-widgets .menu li {
    margin-bottom: 0.4rem;
}

.footer-2[data-grid-type='two-rows-three-columns'] .social-media li {
    margin-bottom: 1rem;
}

.footer-3[data-grid-type='two-rows-three-columns'] {
    padding-top: 3rem;
    border-top: 0.1rem solid var(--color-white);
}

.footer-widgets{
    max-width: 50rem;
}

.footer-widgets h2{
    font: var(--font-headline-5-semibold);
    letter-spacing: var(--font-headline-5--spacing);
    color: var(--color-white);
    margin-bottom: 1rem;
}

.footer-widgets .gform_description{
    display: block;
    font: var(--font-body-small-regular);
    letter-spacing: var(--font-body-small--spacing);
    margin-bottom: 1rem;
}

.footer-widgets .wp-block-buttons{
    margin-bottom: 2rem;
}

.newsletter-form{
    position: relative;
}

body .gform_wrapper.gravity-theme .newsletter-form input{
    border: 0.2rem solid var(--color-white);
    border-radius: 1.2rem;
    padding: 1.1rem 10rem 1.1rem 3rem;
    font-weight: 600;
    color: var(--color-navy);
    font-size: 1.6rem;
    transition: all 0.3s ease;
}

body .gform_wrapper.gravity-theme .newsletter-form input::placeholder{
    color: var(--color-warm-grey);
    text-transform: uppercase;
}

body .gform_wrapper.gravity-theme .newsletter-form .gfield-error input{
    border-color: #F00;
}

body .gform_wrapper.gravity-theme .newsletter-form input:focus{
    outline: none;
    border-color: var(--color-yellow);
}

.newsletter-form button{
    position: absolute;
    top: 0;
    right: 0;
    padding: 0 1rem;
    margin: 0;
    height: 5.4rem;
    border: none;
    border-radius: 1.2rem;
    background: transparent;
    appearance: none;
    font: var(--font-menu);
    letter-spacing: var(--font-menu--spacing);
    color: var(--color-navy);
    text-transform: uppercase;
    cursor: pointer;
}

.newsletter-form button:after{
    content: '';
    display: inline-block;
    width: 1.2rem;
    height: 1.2rem;
    background-image: url('img/newsletter-arrow.svg');
    background-size: contain;
    background-repeat: no-repeat;
    margin-left: 0.5rem;
    margin-right: 0.5rem;
    vertical-align: middle;
    transition: all 0.3s ease;
}

.newsletter-form button:is(:hover, :focus, :active){
    background: transparent;
    color: var(--color-navy);
}

.newsletter-form button:focus{
    outline: none;
}

.newsletter-form button:is(:hover, :focus, :active):after{
    margin-left: 1rem;
    margin-right: 0;
}

body .gform_wrapper.gravity-theme.newsletter-form_wrapper .gform_validation_errors{
    display: none;
}

body .gform_wrapper.gravity-theme .newsletter-form .validation_message{
    border: none;
    padding: 0;
    text-align: center;
    background: transparent;
    color: var(--color-white);
    font-style: italic;
}

.footer-menu .menu,
.footer-widgets .menu{
    display: block;
    margin: 0;
    padding: 0;
}

.footer-menu .menu li,
.footer-widgets .menu li{
    display: block;
    list-style: none;
    margin-bottom: 2rem;
}

.footer-menu .menu li a,
.footer-widgets .menu li a{
    display: block;
    font: var(--font-menu);
    letter-spacing: var(--font-menu--spacing);
    text-transform: uppercase;
}

.social-media{
    margin: 0;
    padding: 0;
}

.social-media li{
    display: block;
    list-style: none;
    margin-bottom: 2rem;
}

.social-media li a{
    font: var(--font-menu);
    letter-spacing: var(--font-menu--spacing);
    text-transform: uppercase;
    display: inline-block;
    text-decoration: none;
}

.social-media li a span{
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-media li a:is(:hover, :focus, :active){
    text-decoration: none;
}

.social-media li a:is(:hover, :focus, :active) span{
    text-decoration: underline;
}

.social-media li a img{
    width: 2rem;
    margin-right: 0.5rem;
}

.footer-3{
    margin-top: 7rem;
}

.copyright{
    font: var(--font-body-small-regular);
    letter-spacing: var(--font-body-small--spacing);
}

.copyright-menu{
    margin: 0 0 1rem;
}

.copyright-menu .menu{
    display: flex;
    align-items: center;
    justify-content: flex-start;
    column-gap: 2rem;
    row-gap: 2rem;
    padding: 0;
    margin: 0;
}

.copyright-menu .menu li{
    display: block;
    list-style: none;
}

.copyright-menu .menu li a{
    font: var(--font-menu);
    letter-spacing: var(--font-menu--spacing);
    text-transform: uppercase;
}

.branding p{
    margin: unset;
}

@media (min-width: 992px){
    .footer-2{
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .footer-widgets:first-child{
        grid-column: span 3;
    }

    .footer-2[data-grid-type='two-rows-three-columns'] .footer-widgets:nth-of-type(2) {
        grid-column: 2/3;
    }

    .footer-2[data-grid-type='two-rows-three-columns'] .footer-widgets:nth-of-type(4) {
        grid-row: 2/3;
        grid-column: 1/2;
        padding-top: unset;
        border-top: unset;
    }

    .footer-3{
        display: flex;
        flex-direction: row-reverse;
        align-items: center;
        justify-content: space-between;
    }

    .copyright-menu{
        margin: unset;
    }

}

@media (min-width: 1200px){
    .topbar-header .container {
        display: grid;
        grid-template-columns: 70% 30%;
    }

    .footer-2{
        grid-template-columns: minmax(0, auto) repeat(3, 20.4rem);
    }

    .footer-2[data-grid-type='two-rows-three-columns'] {
        grid-template-columns: minmax(0, auto) repeat(2, 36rem);
        row-gap: 1.3rem;
        column-gap: 8rem;
    }

    .footer-2[data-grid-type='two-rows-three-columns'] .wp-block-buttons {
        margin-bottom: unset;
    }

    .footer-2[data-grid-type='two-rows-three-columns'] .footer-widgets:is(:nth-of-type(2), :nth-of-type(3)) {
        grid-row: 1/3;
    }

    .footer-3[data-grid-type='two-rows-three-columns'] {
        padding-top: 4.8rem;
    }

    .footer-widgets:first-child{
        grid-column: unset;
    }
}


/* COOKIE DISCLOSURE */
.cookie-disclosure{
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 3rem 0;
    background: var(--color-navy);
    color: var(--color-white);
    z-index: 99999999;
    display: none;
}

.cookie-disclosure a{
    color: var(--color-yellow);
    text-decoration: none;
}

.cookie-disclosure a:is(:hover, :focus, :active){
    text-decoration: underline;
}

.close-cookie-disclosure{
    min-width: 10rem;
}

@media (min-width: 768px){
    .cookie-disclosure-inner{
        display: flex;
        align-items: center;
    }

    .cookie-disclosure-message{
        margin-right: 3rem;
    }

    .cookie-disclosure-message>*:last-child{
        margin-bottom: 0;
    }

    .close-cookie-disclosure{
        margin-left: auto;
    }
}

/* SEARCH RESULTS PAGE */
.blocks-container>h1.search-results, .blocks-container>.results-grid {
    --element-width: var( --container-width);
}

h1.search-results {
    font: var(--font-headline-2-semibold);
    letter-spacing: var(--font-headline-2--spacing);
}

.search-results .results-grid {
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    column-gap: 2.4rem;
    row-gap: 3rem;
}

.search-results .pagination{
    text-align: center;
    margin-top: 3rem;
    grid-column: 2;
}

.search-results .page-numbers{
    font: var(--font-button-semibold);
    letter-spacing: var(--font-button--spacing);
    text-transform: uppercase;
    text-decoration: none;
    border-radius: 1.2rem;
    padding: 1.5rem 1rem;
    min-width: 4rem;
    display: inline-block;
    transition: all 0.3s ease;
    cursor: pointer;
    color: var(--color-grey);
    background-color: var(--color-light-teal);
}

.search-results .page-numbers:is(:hover, :focus, :active, .current){
    background-color: var(--color-yellow);
    text-decoration: none;
    outline: none;
}

.search-results .page-numbers:focus{
    text-decoration: underline dashed;
    text-underline-offset: 0.2em;
}

.search-result-team-member h3 {
    text-align: left;
    font: var(--font-body-large-semibold);
    letter-spacing: var(--font-body-large--spacing);
    margin: 0;
}

.posts-section .team-member .post-card-thumbnail {
    background-color: var(--color-light-blue);
}

.posts-section .team-member .post-card-thumbnail img {
    object-fit: contain;
}

.post-card-meta .search-result-social-media {
    margin: 2rem 0;
    list-style: none;
    padding-left: 0;
}

.post-card-meta a.team-member-bio-open {
    text-transform: uppercase;
}

@media (min-width: 992px){
    h1.search-results {
        margin: 18rem 0 3rem;
    }

    .search-results .results-grid{
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}
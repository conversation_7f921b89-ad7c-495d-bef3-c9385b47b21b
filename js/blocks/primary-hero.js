(function($) {
    $(document).ready(function(){
        if (typeof Swiper == "undefined") return false;

        new Swiper('.primary-hero-slider', {
            loop: true,
            slidesPerView: 1,
            effect: 'fade',
            autoplay: true,
            allowTouchMove: false,
            on: {
                slideChange: function(swiper){
                    let video = $(swiper.el).find('.swiper-slide-next').find('video');
                    if(video.length){
                        video.get(0).currentTime = 0;
                        video.get(0).play();
                    }
                },
                init: function(swiper){
                    $(swiper.el).find('.swiper-pagination .swiper-pagination-bullet').eq(0).removeClass('swiper-pagination-bullet-active');

                    $(swiper.el).find('.swiper-pagination .swiper-pagination-bullet').each(function(index){
                        let slide_time = $(swiper.el).find("[data-swiper-slide-index=" + index + "]").attr('data-swiper-autoplay');
                        $(swiper.el).find('.swiper-pagination .swiper-pagination-bullet').eq(index).css('transition-duration', (slide_time / 1000)+'s');
                    }); 

                    setTimeout(function(){
                        $(swiper.el).find('.swiper-pagination .swiper-pagination-bullet').eq(0).addClass('swiper-pagination-bullet-active'); 
                    }, 1);
                }
            }
        });

    });    
})( jQuery );
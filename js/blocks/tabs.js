(function($) {
    $(document).on('click', '.tabs-section-tab-selector', function(e){
        e.preventDefault();
        change_tab('#'+$(this).siblings('.active').first().attr('id'), '#'+$(this).attr('id'));
    });

    $(document).on('keydown', '.tabs-section-tab-selector', function(e){
        let current_tab = $(this).attr('id');
        let new_tab_element = [];
        switch (e.keyCode){
            case 39: // Left
                new_tab_element = $(this).next('.tabs-section-tab-selector');
                break;
            case 37: // Left
                new_tab_element = $(this).prev('.tabs-section-tab-selector');
                break;
        }
        if(new_tab_element.length){
            let new_tab = new_tab_element.attr('id');
            $('#'+new_tab).focus();
            change_tab('#'+current_tab, '#'+new_tab);
        }
    });

    function change_tab(current_tab, new_tab){
        let current_tabpanel = '#'+$(current_tab).attr('aria-controls');
        $(current_tab).removeClass('active');
        $(current_tab).attr('aria-selected', false);
        $(current_tab).attr('tabindex', "-1");
        $(current_tabpanel).removeClass('active'); 
        $(current_tabpanel).attr('tabindex', "-1");

        let new_tabpanel = '#'+$(new_tab).attr('aria-controls');
        $(new_tab).addClass('active');
        $(new_tab).attr('aria-selected', true);
        $(new_tab).attr('tabindex', "0");
        $(new_tabpanel).addClass('active'); 
        $(new_tabpanel).attr('tabindex', "0");
    }    
})( jQuery );
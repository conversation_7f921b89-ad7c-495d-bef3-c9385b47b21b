(function($) {
    $(document).ready(function(){
        if (typeof Swiper == "undefined") return false;

        const loop = (() => {
            if ($('.team-member-card').length < 2) {
                return false
            }
            if ($('.team-member-card').length < 3) {
                if (matchMedia( "(min-width: 1024px)" ).matches) {
                    return false;
                }
            }
            return true;
        })();

        new Swiper('.team-swiper', {
            loop: loop,
            slidesPerView: 'auto',
            navigation: {
                nextEl: '.team-swiper-next',
                prevEl: '.team-swiper-prev',
            },
            on: {
                beforeInit: (swiper) => {
                    if (swiper.$el[0].querySelectorAll('.swiper-slide').length < 3) return;

                    const getTotalWidth = (swiper) => {
                        let width = 0;
                        swiper.$el[0].querySelectorAll('.swiper-slide').forEach((element) => {
                            width += element.offsetWidth;
                        });
                        return width
                    }

                    // Add cloned Swiper item when the wrapper area less than window inner.
                    while (getTotalWidth(swiper) < window.innerWidth) {
                        swiper.$el[0].querySelectorAll('.swiper-slide').forEach((element) => {
                            let clone = element.cloneNode(true);
                            swiper.$el[0].querySelectorAll('.swiper-wrapper')[0].appendChild(clone);
                        });
                    }
                }
            },
        });

    });
})( jQuery );
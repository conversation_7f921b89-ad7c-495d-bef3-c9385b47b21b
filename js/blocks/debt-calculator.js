(function($) {

    $('.debt-calculator-form .calculator-help').on('click', function(e){
        e.preventDefault();
        $(this).closest('.form-group').find('.form-help').slideToggle();
    });

    function osb_standard(parameters, people){
        const length = Object.keys(parameters).length;
        if(people - 1 < length){
            if(people < 1) people = 1;
            return Number.parseInt(parameters[people]);
        }else{
            return Number.parseInt(parameters[length]);
        }
    }

    $('.debt-calculator-form form').on('submit', function(element){
        element.preventDefault();

        $(this).closest('.debt-calculator-form').hide();
        $('html,body').animate({
            scrollTop: $(this).closest('.debt-calculator-main').offset().top - $('header.header').height() - 100
        }, 500);

        let values = {};
        $.each($(this).serializeArray(), function(i, field) {
            values[field.name] = field.value;
        });

        const a = Number.parseInt(values['unsecured-debt']);
        const b = Number.parseInt(values['net-income']);
        const c = Number.parseInt(values['spousal-net-income']);
        const d = Number.parseInt(values['non-discretionary-expenses']);
        const h = osb_standard(JSON.parse(values['osb-standard']), Number.parseInt(values['people-in-household']));
        const k = Number.parseInt(values['bankrupt-before']);

        const e = (b + c) - d;
        const f = b / (b + c);
        const i = e - h;
        const j = (i * f > 200) ? Number.parseInt( (i * f)/2 ) : 0;
        const z = k ? ( j < 100 ? 24 : 36 ) : ( j < 100 ? 9 : 21 );
        
        const consumer_proposal_settings = JSON.parse(values['consumer-proposal']);
        const bankruptcy_settings = JSON.parse(values['bankruptcy']);
        const regular_payment_settings = JSON.parse(values['regular-payment']);
        const consolidation_loan_settings = JSON.parse(values['consolidation-loan']);
        const credit_counseling_settings = JSON.parse(values['credit-counseling-repayment']);

        let results = {
            consumer_proposal: {
                monthly_payment: Math.ceil((Math.max((j * z), (a * (consumer_proposal_settings.estimated_return / 100)), consumer_proposal_settings.minimum_amount))/consumer_proposal_settings.maximum_term),
                total_payment: Math.ceil((Math.max((j * z), (a * (consumer_proposal_settings.estimated_return / 100)), consumer_proposal_settings.minimum_amount))),
                interest_rate: null,
                payback_period: consumer_proposal_settings.maximum_term,
            },
            bankruptcy: {
                monthly_payment: Math.ceil((Math.max((j * z), bankruptcy_settings.minimum_cost) / z), 100),
                total_payment: Math.ceil(Math.max((j * z), bankruptcy_settings.minimum_cost)),
                interest_rate: null,
                payback_period: z,
            },
            regular_payment: {
                monthly_payment: (a * ( (((regular_payment_settings.interest_rate / 100) / 12) * Math.pow(1 + ((regular_payment_settings.interest_rate / 100) / 12), regular_payment_settings.payback_time)) / (Math.pow(1 + ((regular_payment_settings.interest_rate / 100) / 12), regular_payment_settings.payback_time) - 1) )).toFixed(2),
                total_payment: (a * ( (((regular_payment_settings.interest_rate / 100) / 12) * Math.pow(1 + ((regular_payment_settings.interest_rate / 100) / 12), regular_payment_settings.payback_time)) / (Math.pow(1 + ((regular_payment_settings.interest_rate / 100) / 12), regular_payment_settings.payback_time) - 1) )).toFixed(2) * regular_payment_settings.payback_time,
                interest_rate: regular_payment_settings.interest_rate,
                payback_period: regular_payment_settings.payback_time,
            },
            consolidation_loan: {
                monthly_payment: (a * ( (((consolidation_loan_settings.interest_rate / 100) / 12) * Math.pow(1 + ((consolidation_loan_settings.interest_rate / 100) / 12), consolidation_loan_settings.payback_time)) / (Math.pow(1 + ((consolidation_loan_settings.interest_rate / 100) / 12), consolidation_loan_settings.payback_time) - 1) )).toFixed(2),
                total_payment: (a * ( (((consolidation_loan_settings.interest_rate / 100) / 12) * Math.pow(1 + ((consolidation_loan_settings.interest_rate / 100) / 12), consolidation_loan_settings.payback_time)) / (Math.pow(1 + ((consolidation_loan_settings.interest_rate / 100) / 12), consolidation_loan_settings.payback_time) - 1) )).toFixed(2) * consolidation_loan_settings.payback_time,
                interest_rate: consolidation_loan_settings.interest_rate,
                payback_period: consolidation_loan_settings.payback_time,
            },
            credit_counseling_repayment: {
                monthly_payment: (a * ( (((credit_counseling_settings.interest_rate / 100) / 12) * Math.pow(1 + ((credit_counseling_settings.interest_rate / 100) / 12), credit_counseling_settings.payback_time)) / (Math.pow(1 + ((credit_counseling_settings.interest_rate / 100) / 12), credit_counseling_settings.payback_time) - 1) )).toFixed(2),
                total_payment: (a * ( (((credit_counseling_settings.interest_rate / 100) / 12) * Math.pow(1 + ((credit_counseling_settings.interest_rate / 100) / 12), credit_counseling_settings.payback_time)) / (Math.pow(1 + ((credit_counseling_settings.interest_rate / 100) / 12), credit_counseling_settings.payback_time) - 1) )).toFixed(2) * credit_counseling_settings.payback_time,
                interest_rate: credit_counseling_settings.interest_rate,
                payback_period: credit_counseling_settings.payback_time,
            }
        };

        let formated_results = format_debt_calculator_results(values, results);

        display_debt_calculator_results(element.target, results, formated_results)
    });

    function format_debt_calculator_results(values, results){
        let formated_results = {
            consumer_proposal: format_debt_calculator_results_object(values, results.consumer_proposal, JSON.parse(values['consumer-proposal'])),
            bankruptcy: format_debt_calculator_results_object(values, results.bankruptcy, JSON.parse(values['bankruptcy'])),
            regular_payment: format_debt_calculator_results_object(values, results.regular_payment, JSON.parse(values['regular-payment'])),
            consolidation_loan: format_debt_calculator_results_object(values, results.consolidation_loan, JSON.parse(values['consolidation-loan'])),
            credit_counseling_repayment: format_debt_calculator_results_object(values, results.credit_counseling_repayment, JSON.parse(values['credit-counseling-repayment'])),
        }

        return format_debt_calculator_description(values, formated_results);
    }

    function format_debt_calculator_results_object(values, results, settings){
        const label_years = values['years-label'];
        const label_months = values['months-label'];
        const label_none = values['none-label'];
        const price_formatter = new Intl.NumberFormat('en-CA', {
            style: 'currency',
            currency: 'CAD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        });

        return {
            monthly_payment: price_formatter.format(results.monthly_payment),
            total_payment: price_formatter.format(results.total_payment),
            interest_rate: (results.interest_rate) ? results.interest_rate+'%' : label_none,
            payback_period: results.payback_period / settings.payback_unit + ' ' + (settings.payback_unit == 1 ? label_months : label_years),
        }
    }

    function format_debt_calculator_description(values, formated_results){
        formated_results.consumer_proposal.description = replace_debit_calculator_description(values['consumer-proposal-description'], formated_results.consumer_proposal);
        formated_results.bankruptcy.description = replace_debit_calculator_description(values['bankruptcy-description'], formated_results.bankruptcy);
        formated_results.regular_payment.description = replace_debit_calculator_description(values['regular-payment-description'], formated_results.regular_payment);
        formated_results.consolidation_loan.description = replace_debit_calculator_description(values['consolidation-loan-description'], formated_results.consolidation_loan);
        formated_results.credit_counseling_repayment.description = replace_debit_calculator_description(values['credit-counseling-repayment-description'], formated_results.credit_counseling_repayment);
        return formated_results;
    }

    function replace_debit_calculator_description(description, formated_results){
        return replaceBulk(
            description, 
            [
                '{monthly_payment}', '{total_payment}', '{interest_rate}', '{payback_period}'
            ], 
            [
                formated_results.monthly_payment, 
                formated_results.total_payment, 
                formated_results.interest_rate, 
                formated_results.payback_period
            ]
        );
    }

    function display_debt_calculator_results(form, results, formated_results){
        update_debt_calculator_graphic(form, results, formated_results);

        const calculator_results = $(form).closest('.debt-calculator-form').siblings('.debt-calculator-results');

        fill_debt_calculator_results_info(calculator_results, formated_results.consumer_proposal, 'consumer-proposal');
        fill_debt_calculator_results_info(calculator_results, formated_results.bankruptcy, 'bankruptcy');
        fill_debt_calculator_results_info(calculator_results, formated_results.regular_payment, 'regular-payment');
        fill_debt_calculator_results_info(calculator_results, formated_results.consolidation_loan, 'consolidation-loan');
        fill_debt_calculator_results_info(calculator_results, formated_results.credit_counseling_repayment, 'credit-counseling-repayment');

        calculator_results.show();
    }

    function update_debt_calculator_graphic(form, results, formated_results){
        let limit = 0;
        $.each(results, function(index, element){
            if(element.total_payment > limit) limit = element.total_payment;
        });

        const calculator_results = $(form).closest('.debt-calculator-form').siblings('.debt-calculator-results');
        fill_debt_calculator_graphic_info(calculator_results, results.consumer_proposal, formated_results.consumer_proposal, limit, 'consumer-proposal');
        fill_debt_calculator_graphic_info(calculator_results, results.bankruptcy, formated_results.bankruptcy, limit, 'bankruptcy');
        fill_debt_calculator_graphic_info(calculator_results, results.regular_payment, formated_results.regular_payment, limit, 'regular-payment');
        fill_debt_calculator_graphic_info(calculator_results, results.consolidation_loan, formated_results.consolidation_loan, limit, 'consolidation-loan');
        fill_debt_calculator_graphic_info(calculator_results, results.credit_counseling_repayment, formated_results.credit_counseling_repayment, limit, 'credit-counseling-repayment');
    }

    function fill_debt_calculator_graphic_info(calculator_results, results, formated_results, limit, class_name){
        calculator_results.find('.debt-calculator-results-bar-'+class_name).css('height', ((results.total_payment / limit) * 100)+'%');
        calculator_results.find('.debt-calculator-results-bar-'+class_name).find('.debt-calculator-results-bar-total').html(formated_results.total_payment);
        calculator_results.find('.debt-calculator-results-bar-'+class_name).find('.debt-calculator-results-bar-description p').html(formated_results.description);
    }

    function fill_debt_calculator_results_info(calculator_results, formated_results, class_name){
        calculator_results.find('.'+class_name+'-description').html(formated_results.description);
        calculator_results.find('.'+class_name+'-monthly-payment').html(formated_results.monthly_payment);
        calculator_results.find('.'+class_name+'-total-payment').html(formated_results.total_payment);
        calculator_results.find('.'+class_name+'-interest-rate').html(formated_results.interest_rate);
        calculator_results.find('.'+class_name+'-payback-period').html(formated_results.payback_period);
    }

    // https://stackoverflow.com/questions/5069464/replace-multiple-strings-at-once
    function replaceBulk( str, findArray, replaceArray ){
        var i, regex = [], map = {}; 
        for( i=0; i<findArray.length; i++ ){ 
            regex.push( findArray[i].replace(/([-[\]{}()*+?.\\^$|#,])/g,'\\$1') );
            map[findArray[i]] = replaceArray[i]; 
        }
        regex = regex.join('|');
        str = str.replace( new RegExp( regex, 'g' ), function(matched){
            return map[matched];
        });
        return str;
    }
    
    $('.debt-calculator-results .debt-calculator-print').on('click', function(e){
        e.preventDefault();
        
        $('.calculator-print').removeClass('calculator-print');
        $('.area-to-print').removeClass('area-to-print');
        $('body').addClass('calculator-print');
        $(this).closest('.debt-calculator').addClass('area-to-print');
        window.print();
    });
    
    $('.debt-calculator-results .debt-calculator-try-again').on('click', function(e){
        e.preventDefault();
        $(this).closest('.debt-calculator-results').hide();
        $(this).closest('.debt-calculator-results').siblings('.debt-calculator-form').show();
        
        $('html,body').animate({
            scrollTop: $(this).closest('.debt-calculator-main').offset().top - $('header.header').height() - 100
        }, 500);
    });


})( jQuery );
(function($) {
    function displayTeamMembersLoading(block, hide = true){
        if(hide) $('#'+block+' .team-members-inner').hide();
        $('#'+block+' .team-members-loading').show();
    }

    function updateTeamMembers(html, block){
        $('html,body').animate({
            scrollTop: $('#'+block).position().top
        }, 500);

        $('#'+block+' .team-members-inner').html(html);
        $('#'+block+' .team-members-loading').hide();
        $('#'+block+' .team-members-inner').show();
    }

    function debounce(func, delay) {
    let timeout;
    return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), delay);
    };
}

    function fetchResults($form, block) {
        const params = $form.serialize();

        displayTeamMembersLoading(block);

        $.ajax({
            url: '/wp-admin/admin-ajax.php?action=team_member_filter&' + params,
            method: 'GET',
            success: function(response){
                if (response.success) {
                    updateTeamMembers(response.data, block);
                } else {
                    console.error('AJAX returned error');
                }
            },
            error: function(xhr) {
                console.error('AJAX load failed:', xhr.status, xhr.statusText);
            }
        });
    }

    $('.team-members-filter select').on('change', function () {
        const $form = $(this).closest('form');
        const block = $(this).closest('.team-members').attr('id');
        fetchResults($form, block);
    });

    $('.team-members-search-input').on('input', debounce(function () {
        const $form = $(this).closest('form');
        const block = $(this).closest('.team-members').attr('id');
        fetchResults($form, block);
    }, 300));

    $('.team-members-search-input').on('keypress', function(e) {
        if (e.which === 13) {
            e.preventDefault();
            $(this).trigger('input');
        }
    });

})(jQuery);
(function($) {
    function initMap( $el ) {
        var $markers = $el.find('.marker');

        var mapArgs = {
            zoom        : $el.data('zoom') || 16,
            mapTypeId   : google.maps.MapTypeId.ROADMAP,
            styles      : [ { "featureType": "all", "stylers": [ { "saturation": 0 }, { "hue": "#e7ecf0" } ] }, { "featureType": "road", "stylers": [ { "saturation": -70 } ] }, { "featureType": "transit", "stylers": [ { "visibility": "off" } ] }, { "featureType": "poi", "stylers": [ { "visibility": "off" } ] }, { "featureType": "water", "stylers": [ { "visibility": "simplified" }, { "saturation": -60 } ] } ]
        };
        var map = new google.maps.Map( $el[0], mapArgs );

        map.markers = [];
        $markers.each(function(){
            initMarker( $(this), map );
        });

        centerMap( map );

        return map;
    }

    function initMarker( $marker, map ) {
        var lat = $marker.data('lat');
        var lng = $marker.data('lng');
        var icon = $marker.data('marker');
        var latLng = {
            lat: parseFloat( lat ),
            lng: parseFloat( lng )
        };

        var marker = new google.maps.Marker({
            position : latLng,
            map: map,
            icon: icon
        });

        map.markers.push( marker );

        if( $marker.html() ){
            var infowindow = new google.maps.InfoWindow({
                content: $marker.html()
            });

            google.maps.event.addListener(marker, 'click', function() {
                infowindow.open( map, marker );
            });
        }else{
            google.maps.event.addListener(marker, 'click', function() {
                window.open(`https://maps.google.com/maps?q=${lat},${lng}`, '_blank');
            });
        }
    }

    function centerMap( map ) {
        var bounds = new google.maps.LatLngBounds();
        map.markers.forEach(function( marker ){
            bounds.extend({
                lat: marker.position.lat(),
                lng: marker.position.lng()
            });
        });

        if( map.markers.length == 1 ){
            map.setCenter( bounds.getCenter() );

        } else{
            map.fitBounds( bounds );
        }
    }

    $(document).ready(function(){
        if($('.content-with-media .map').length){
            let key = $('.content-with-media .map').first().data('key');

            $.getScript({
                url: 'https://maps.googleapis.com/maps/api/js?key='+key,
                success: function(){
                    $('.content-with-media .map').each(function(){
                        initMap( $(this) );
                    });
                }
            })
        }
    });
})( jQuery );
(function($) {
    $(document).ready(function(){
        if (typeof Swiper == "undefined") return false;

        let slides = [];

        $.each($('.testimonials-carousel-image-swiper .swiper-wrapper').children(), function(i, e){
            slides.push([
                $('.testimonials-carousel-image-swiper .swiper-wrapper').children()[i],
                $('.testimonials-carousel-swiper .swiper-wrapper').children()[i],
            ]);
        });

        $('.testimonials-carousel-image-swiper .swiper-wrapper').empty();
        $('.testimonials-carousel-swiper .swiper-wrapper').empty();

        slides.sort(function() {
            return 0.5 - Math.random();
        });

        slides.forEach(function(element){
            $('.testimonials-carousel-image-swiper .swiper-wrapper').append(element[0]);
            $('.testimonials-carousel-swiper .swiper-wrapper').append(element[1]);
        });

        let images = new Swiper('.testimonials-carousel-image-swiper', {
            loop: false,
            slidesPerView: 1,
            simulateTouch: false,
            allowTouchMove: false,
            effect: 'fade',
        });

        new Swiper('.testimonials-carousel-swiper', {
            loop: true,
            slidesPerView: 1,
            spaceBetween: 50,
            thumbs: {
                swiper: images,
            },
            navigation: {
                nextEl: '.testimonials-carousel-quote-next',
                prevEl: '.testimonials-carousel-quote-prev',
            },
        });
    });    
})( jQuery );
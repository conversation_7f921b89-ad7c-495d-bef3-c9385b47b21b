(function($) {

    // Header height
    function setHeaderHeight(){
        let height = $('.header').outerHeight();
        $(':root').css('--header-height', height+'px');
    }
    setHeaderHeight();
    setTimeout(setHeaderHeight, 350);
    $(document).ready(setHeaderHeight);

    $(window).on('resize', function(){
        setHeaderHeight();
        setTimeout(setHeaderHeight, 100);
        setTimeout(setHeaderHeight, 150);
        setTimeout(setHeaderHeight, 200);
        setTimeout(setHeaderHeight, 250);
        setTimeout(setHeaderHeight, 300);
        setTimeout(setHeaderHeight, 350);
    });

    // Fixed navbar
    let last_scroll_position = 0;
    function showFixedNavbar(){
        let current_scroll_position = $(this).scrollTop();
        if(current_scroll_position > 0){
            $("body").addClass("scrolled");
        }else{
            $("body").removeClass("scrolled");
        }

        if(current_scroll_position > $('header.header').height() + ($('#wpadminbar').height() || 0)){
            $("body").addClass("fixed-navbar");
        }else{
            $("body").removeClass("fixed-navbar");
        }

        if(current_scroll_position < last_scroll_position){
            $('body').addClass('scrolling-up');
        }else{
            $('body').removeClass('scrolling-up');
        }

        last_scroll_position = current_scroll_position;
    }
    showFixedNavbar();

    $(window).on("scroll", showFixedNavbar);


    // Lazy BG
    let lazy_bg_loaded = false;
    function loadLazyBg(){
        if($(window).scrollTop() > 0){
            lazy_bg_loaded = true;
            $("body").addClass('load-lazy-bg');
        }
    }

    loadLazyBg();

    $(window).on("scroll", function(){
        if(!lazy_bg_loaded){
            loadLazyBg();
        }
    });


    // Mail click to fetch email
    $(document).on('click', '.email-link', function(event) {
        event.preventDefault();

        const memberName = $(this).data('name');

        fetch(`/wp-json/your-endpoint/v1/get-email`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Custom-Token': 'd4c9a7c8b2e1f4a5d6c3b7a2e5f8d9a0'
            },
            body: JSON.stringify({ name: memberName })
        })
        .then(response => response.json())
        .then(data => {
            if (data.email) {
                window.location.href = `mailto:${data.email}`;
            } else {
                alert('Email not available.');
            }
        })
        .catch(error => console.error('Error:', error));
    });
    

    //Animate scroll
    function animateScroll(dest){
        if($(dest).length){
            var scrollPosition = $(dest).offset().top - 99;
            $('html,body').animate({
                scrollTop: scrollPosition
            }, 500);
        }
    };


    //Animate scroll on home page menu links
    $('a:not(".ignore")').on("click", function(e){
        var url = $(this).attr('href');
        if(typeof url == "undefined") return;
        if($(this).parent().attr('role') == 'tab') return;
        if(url.indexOf('#') == 0 || (url.indexOf('#') > -1 && url.indexOf(window.location.href.split('#')[0]) == 0)){
            var dest = "#"+url.substring(url.indexOf('#')+1);
            
            if($(dest).length){
                e.preventDefault();
                animateScroll(dest);
            }
        }
    });


    // Animations
    $.fn.shouldAnimate = function(){
        var win = $(window);
        var topOffset = win.innerHeight() / 8;

        var viewport = {
            top : win.scrollTop() - topOffset,
            left : win.scrollLeft()
        };

        viewport.right = viewport.left + win.width();
        viewport.bottom = viewport.top + win.height();

        var bounds = this.offset();
        bounds.right = bounds.left + this.outerWidth();
        bounds.bottom = bounds.top + this.outerHeight();

        return (!(viewport.right < bounds.left || viewport.left > bounds.right || viewport.bottom < bounds.top /*|| viewport.top > bounds.bottom*/));
    };

    function runAnimations(){
        $.each($('[data-animation]:not(.animated),.animate:not(.animated)'), function(index, element){
            if($(this).shouldAnimate()){
                $(this).addClass('animated');
                if($(this).data('animation')){
                    $(this).addClass($(this).data('animation'));
                }else{
                    let element = $(this);
                    $.each($(this).attr('class').split(/\s+/), function(index, value){
                        if(value.indexOf('animate-') == 0){
                            element.addClass(value.substring(8));
                        }
                    });
                }
            }
        });
    }

    runAnimations();

    $(document).on('scroll', function(){
        runAnimations();
    });


    // Menu
    $('.toggle-menu').on('click', function(e){
        e.preventDefault();
        $('body').toggleClass('menu-open');
    });

    $('.main-menu a').on('click', function(e){
        $('body').removeClass('menu-open');
    });

    // Mobile menu
    $('.sub-menu-toggle').on('click', function(e){
        $(this).closest('.menu-item-has-children').toggleClass('sub-menu-open');
        $(this).siblings('.sub-menu').slideToggle();
    });

    $('.menu-item-has-children>a[href="#"]').on('click', function(e){
        if($(this).siblings('.sub-menu-toggle').length){
            e.preventDefault();
            $(this).siblings('.sub-menu-toggle').trigger('click');
        }
    });


    // Cookie disclosure
    $('.close-cookie-disclosure').on('click', function(e){
        e.preventDefault();
        $(this).closest('.cookie-disclosure').hide();
        localStorage.setItem('cookie_disclosure', true);
    });

    if(!localStorage.getItem('cookie_disclosure')){
        $('.cookie-disclosure').show();
    }


    // Share link
    $(".share-link:not(.share-to-clipboard)").on("click", function(e){
        e.preventDefault();
        var url = $(this).attr("href");
        window.open(url, "Share", "width=600,height=400");
    });

    $(".share-link.share-to-clipboard").on("click", async function(e){
        e.preventDefault();
        var url = $(this).attr("href");
        await navigator.clipboard.writeText(url);
    });


    // Custom form file input
    $(document).ready(function(){
        $('.form-file-input input[type="file"]').each(function(){
            let id = $(this).attr('id');
            $(this).after('<span class="selected-file">No file chosen</span>');
            $(this).after('<label for="'+id+'" class="file-upload-button"><span>Select File</span><svg width="16" height="19" viewBox="0 0 16 19" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16 9.37695L8 9.37695L0 9.37695L8 0.620197L16 9.37695Z" fill="#F2673A"/><rect y="12.377" width="16" height="2" fill="#F2673A"/><rect y="17.377" width="16" height="1" fill="#F2673A"/></svg></label>');
            $(this).hide();

            $(this).on('change', function(){
                if($(this).val() != ""){
                    let filename = $(this).val();
                    filename = filename.split(/(\\|\/)/g).pop();
                    $(this).siblings('.selected-file').html(filename);
                    $(this).siblings('.selected-file').addClass('has-file');
                }else{
                    $(this).siblings('.selected-file').html('No file chosen');
                    $(this).siblings('.selected-file').removeClass('has-file');
                }
            });
        });
    });


    // Allow select styling based on value
    $(document).on('change', 'select', function(){
        $(this).attr('data-chosen', $(this).val());
    });


    // Gravity Forms scroll
    $('.gform_wrapper').on('DOMNodeInserted', function(e) {
        if ( $(e.target).hasClass('gform_validation_errors') ) {
            let field_id = $(e.target).closest('.gform_wrapper').find('.gfield_error').attr('id');
            animateScroll('#'+field_id);
        }
    });

    // Open Search Bar when click on search icon
    $('.top-menu-search a').on('click', function(e){
        e.preventDefault();
        var input = $('.top-search-bar input');
        var currentOpacity = input.css('display');
        var newOpacity = currentOpacity == 'grid' ? 'none' : 'grid';
        input.css('display', newOpacity);
    });


})( jQuery );
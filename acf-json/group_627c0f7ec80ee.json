{"key": "group_627c0f7ec80ee", "title": "Block: Call to Action", "fields": [{"key": "field_627c0fa89ed31", "label": "Eyebrow", "name": "eyebrow", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_627c0fc69ed32", "label": "Headline", "name": "headline", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 2, "new_lines": "br"}, {"key": "field_627c0fcf9ed33", "label": "<PERSON><PERSON>", "name": "button", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array"}, {"key": "field_627c0fd79ed34", "label": "Background", "name": "background", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "url", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}], "location": [[{"param": "block", "operator": "==", "value": "acf/call-to-action"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1652297685}
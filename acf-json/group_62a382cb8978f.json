{"key": "group_62a382cb8978f", "title": "Block: Debt Calculator", "fields": [{"key": "field_62a383aa9ac62", "label": "Eyebrow", "name": "eyebrow", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a382e1df38c", "label": "Title", "name": "title", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 2, "new_lines": "br"}, {"key": "field_62a382f2df38d", "label": "Introduction", "name": "intro", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 0}, {"key": "field_62a382fadf38e", "label": "Calculator Settings", "name": "calculator", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_62a393c876419", "label": "Form Fields", "name": "", "type": "accordion", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "open": 1, "multi_expand": 1, "endpoint": 0}, {"key": "field_62a39825b97bf", "label": "Total Unsecured Debt", "name": "total_unsecured_debt", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_62a39851b97c0", "label": "Label", "name": "label", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a39859b97c1", "label": "Help", "name": "help", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 3, "new_lines": "br"}, {"key": "field_62a39863b97c2", "label": "Placeholder", "name": "placeholder", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}]}, {"key": "field_62a398a4686d0", "label": "Monthly Net Income After Tax", "name": "monthly_net_income_after_tax", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_62a398a4686d1", "label": "Label", "name": "label", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a398a4686d2", "label": "Help", "name": "help", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 3, "new_lines": "br"}, {"key": "field_62a398a4686d3", "label": "Placeholder", "name": "placeholder", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}]}, {"key": "field_62a398b3686d4", "label": "Spousal Monthly Net Income After Tax", "name": "spousal_monthly_net_income_after_tax", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_62a398b3686d5", "label": "Label", "name": "label", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a398b3686d6", "label": "Help", "name": "help", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 3, "new_lines": "br"}, {"key": "field_62a398b3686d7", "label": "Placeholder", "name": "placeholder", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}]}, {"key": "field_62a398c2686d8", "label": "Monthly Non-discretionary Expenses", "name": "monthly_non-discretionary_expenses", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_62a398c2686d9", "label": "Label", "name": "label", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a398c2686da", "label": "Help", "name": "help", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 3, "new_lines": "br"}, {"key": "field_62a398c2686db", "label": "Placeholder", "name": "placeholder", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}]}, {"key": "field_62a39c0676c24", "label": "Number of People in Household", "name": "number_of_people_in_household", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_62a39c0676c25", "label": "Label", "name": "label", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a39c0676c26", "label": "Help", "name": "help", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 3, "new_lines": "br"}, {"key": "field_62a39c0676c27", "label": "Placeholder", "name": "placeholder", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}]}, {"key": "field_62a39c1a76c29", "label": "Have You Ever Been Bankrupt Before?", "name": "have_you_ever_been_bankrupt_before", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_62a39c1a76c2a", "label": "Label", "name": "label", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a39c1a76c2b", "label": "Help", "name": "help", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 3, "new_lines": "br"}]}, {"key": "field_62a3adc602953", "label": "Parameters", "name": "", "type": "accordion", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "open": 1, "multi_expand": 1, "endpoint": 0}, {"key": "field_62a3ade002954", "label": "OSB Standard", "name": "osb_standard", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_62a3adeb02955", "label": "1", "name": "1", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": "", "max": 0, "step": 1}, {"key": "field_62a3adfb02956", "label": "2", "name": "2", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": "", "max": 0, "step": 1}, {"key": "field_62a3adfe02957", "label": "3", "name": "3", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": "", "max": 0, "step": 1}, {"key": "field_62a3ae0002958", "label": "4", "name": "4", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": "", "max": 0, "step": 1}, {"key": "field_62a3ae0102959", "label": "5", "name": "5", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": "", "max": 0, "step": 1}, {"key": "field_62a3ae030295a", "label": "6", "name": "6", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": "", "max": 0, "step": 1}, {"key": "field_62a3ae050295b", "label": "6+", "name": "7", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": "", "max": 0, "step": 1}]}, {"key": "field_62a75c335a540", "label": "Consumer Proposal", "name": "consumer_proposal_parameters", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_62a75cbe5a542", "label": "Payback Unit", "name": "payback_unit", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"1": "Months", "12": "Years"}, "default_value": false, "allow_null": 0, "multiple": 0, "ui": 0, "return_format": "value", "ajax": 0, "placeholder": ""}, {"key": "field_62a77b82f2774", "label": "Minimum Amount", "name": "minimum_amount", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": 0, "max": "", "step": 1}, {"key": "field_62a77b9cf2775", "label": "Maximum Term (months)", "name": "maximum_term", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": 0, "max": "", "step": 1}, {"key": "field_62bcb0b4a328f", "label": "Estimated Return", "name": "estimated_return", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": "", "max": "", "step": 1}]}, {"key": "field_62a77c7cd56ff", "label": "Bankruptcy", "name": "bankruptcy_parameters", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_62a77c7cd5700", "label": "Payback Unit", "name": "payback_unit", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"1": "Months", "12": "Years"}, "default_value": false, "allow_null": 0, "multiple": 0, "ui": 0, "ajax": 0, "return_format": "value", "placeholder": ""}, {"key": "field_62a77c7cd5701", "label": "Minimum Administration Cost", "name": "minimum_cost", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": 0, "max": "", "step": 1}]}, {"key": "field_62a7898655cfc", "label": "Regular Payment", "name": "regular_payment_parameters", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_62a7898655cfd", "label": "Payback Unit", "name": "payback_unit", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"1": "Months", "12": "Years"}, "default_value": false, "allow_null": 0, "multiple": 0, "ui": 0, "ajax": 0, "return_format": "value", "placeholder": ""}, {"key": "field_62a789b255d00", "label": "Payback Time (months)", "name": "payback_time", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": 0, "max": "", "step": 1}, {"key": "field_62a7899d55cff", "label": "Interest Rate", "name": "interest_rate", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": 0, "max": "", "step": 1}]}, {"key": "field_62a789c655d01", "label": "Consolidation Loan", "name": "consolidation_loan_parameters", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_62a789c655d02", "label": "Payback Unit", "name": "payback_unit", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"1": "Months", "12": "Years"}, "default_value": false, "allow_null": 0, "multiple": 0, "ui": 0, "ajax": 0, "return_format": "value", "placeholder": ""}, {"key": "field_62a789c655d03", "label": "Payback Time (months)", "name": "payback_time", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": 0, "max": "", "step": 1}, {"key": "field_62a789c655d04", "label": "Interest Rate", "name": "interest_rate", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": 0, "max": "", "step": 1}]}, {"key": "field_62a789d055d05", "label": "Credit Counseling Repayment", "name": "credit_counseling_repayment_parameters", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_62a789d055d06", "label": "Payback Unit", "name": "payback_unit", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"1": "Months", "12": "Years"}, "default_value": false, "allow_null": 0, "multiple": 0, "ui": 0, "ajax": 0, "return_format": "value", "placeholder": ""}, {"key": "field_62a789d055d07", "label": "Payback Time (months)", "name": "payback_time", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": 0, "max": "", "step": 1}, {"key": "field_62a789d055d08", "label": "Interest Rate", "name": "interest_rate", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": 0, "max": "", "step": 1}]}, {"key": "field_62a75ae5fa2d1", "label": "Labels", "name": "", "type": "accordion", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "open": 0, "multi_expand": 0, "endpoint": 0}, {"key": "field_62a75af7fa2d2", "label": "Consumer Proposal", "name": "consumer_proposal_labels", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_62a7629b98be6", "label": "Title", "name": "title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a75b0dfa2d3", "label": "Description", "name": "description", "type": "textarea", "instructions": "You can use the following shortcodes: {monthly_payment}, {total_payment}, {interest_rate}, {payback_period}", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 4, "new_lines": "br"}, {"key": "field_62a75b48fa2d4", "label": "Monthly Payment", "name": "monthly_payment", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a75b50fa2d5", "label": "Total Payment", "name": "total_payment", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a75b56fa2d6", "label": "Interest Rate", "name": "interest_rate", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a75b5dfa2d7", "label": "Payback Period", "name": "payback_period", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}]}, {"key": "field_62a77cbdd5703", "label": "Bankruptcy", "name": "bankruptcy_labels", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_62a77cbdd5704", "label": "Title", "name": "title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a77cbdd5705", "label": "Description", "name": "description", "type": "textarea", "instructions": "You can use the following shortcodes: {monthly_payment}, {total_payment}, {interest_rate}, {payback_period}", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 4, "new_lines": "br"}, {"key": "field_62a77cbdd5706", "label": "Monthly Payment", "name": "monthly_payment", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a77cbdd5707", "label": "Total Payment", "name": "total_payment", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a77cbdd5708", "label": "Interest Rate", "name": "interest_rate", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a77cbdd5709", "label": "Payback Period", "name": "payback_period", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}]}, {"key": "field_62a78699af50e", "label": "Regular Payment", "name": "regular_payment_labels", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_62a78699af50f", "label": "Title", "name": "title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a78699af510", "label": "Description", "name": "description", "type": "textarea", "instructions": "You can use the following shortcodes: {monthly_payment}, {total_payment}, {interest_rate}, {payback_period}", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 4, "new_lines": "br"}, {"key": "field_62a78699af511", "label": "Monthly Payment", "name": "monthly_payment", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a78699af512", "label": "Total Payment", "name": "total_payment", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a78699af513", "label": "Interest Rate", "name": "interest_rate", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a78699af514", "label": "Payback Period", "name": "payback_period", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}]}, {"key": "field_62a786a6af515", "label": "Consolidation Loan", "name": "consolidation_loan_labels", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_62a786a6af516", "label": "Title", "name": "title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a786a6af517", "label": "Description", "name": "description", "type": "textarea", "instructions": "You can use the following shortcodes: {monthly_payment}, {total_payment}, {interest_rate}, {payback_period}", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 4, "new_lines": "br"}, {"key": "field_62a786a6af518", "label": "Monthly Payment", "name": "monthly_payment", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a786a6af519", "label": "Total Payment", "name": "total_payment", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a786a6af51a", "label": "Interest Rate", "name": "interest_rate", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a786a6af51b", "label": "Payback Period", "name": "payback_period", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}]}, {"key": "field_62a786b0af51c", "label": "Credit Counseling Repayment", "name": "credit_counseling_repayment_labels", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_62a786b0af51d", "label": "Title", "name": "title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a786b0af51e", "label": "Description", "name": "description", "type": "textarea", "instructions": "You can use the following shortcodes: {monthly_payment}, {total_payment}, {interest_rate}, {payback_period}", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 4, "new_lines": "br"}, {"key": "field_62a786b0af51f", "label": "Monthly Payment", "name": "monthly_payment", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a786b0af520", "label": "Total Payment", "name": "total_payment", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a786b0af521", "label": "Interest Rate", "name": "interest_rate", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a786b0af522", "label": "Payback Period", "name": "payback_period", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}]}]}, {"key": "field_62a38307df38f", "label": "Disclaimer", "name": "disclaimer", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 0}], "location": [[{"param": "block", "operator": "==", "value": "acf/debt-calculator"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1656533178}
{"key": "group_627d247f31b3f", "title": "Block: Posts Cards", "fields": [{"key": "field_627d24a28d4e8", "label": "Eyebrow", "name": "eyebrow", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_627d24a98d4e9", "label": "Title", "name": "title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_627d24af8d4ea", "label": "<PERSON><PERSON>", "name": "button", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array"}, {"key": "field_627d252c8d4ed", "label": "Post Types", "name": "post_types", "type": "checkbox", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "choices": {"post": "Posts", "project": "Projects", "success_story": "Success Stories"}, "allow_custom": 0, "default_value": ["post"], "layout": "vertical", "toggle": 0, "return_format": "value", "save_custom": 0}, {"key": "field_627d24fb8d4ec", "label": "Quantity", "name": "limit_posts", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "choices": {"2": "2", "3": "3"}, "default_value": 3, "allow_null": 0, "multiple": 0, "ui": 0, "return_format": "value", "ajax": 0, "placeholder": ""}, {"key": "field_627d24b78d4eb", "label": "Posts", "name": "posts", "type": "post_object", "instructions": "Leave empty to display the most recent ones", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "post_type": ["post", "project"], "taxonomy": "", "allow_null": 1, "multiple": 1, "return_format": "id", "ui": 1}, {"key": "field_627d2a96b0fda", "label": "Services", "name": "services", "type": "taxonomy", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "taxonomy": "service", "field_type": "multi_select", "allow_null": 1, "add_term": 1, "save_terms": 0, "load_terms": 0, "return_format": "id", "multiple": 0}, {"key": "field_627d2ab4b0fdb", "label": "Industries", "name": "industries", "type": "taxonomy", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "taxonomy": "industry", "field_type": "multi_select", "allow_null": 1, "add_term": 1, "save_terms": 0, "load_terms": 0, "return_format": "id", "multiple": 0}], "location": [[{"param": "block", "operator": "==", "value": "acf/posts-cards"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1652974931}
{"key": "group_6283c264888bf", "title": "Block: Posts", "fields": [{"key": "field_6283c2a61500c", "label": "Title", "name": "title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_6283c2bf1500d", "label": "Enable Filters", "name": "enable_filters", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "Check to enable post filtering", "default_value": 1, "ui": 0, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_6283c344cd8b0", "label": "Filter Button Label", "name": "filter_button_label", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_6283c2bf1500d", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_628410abf599b", "label": "Filters", "name": "filters", "type": "select", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_6283c2bf1500d", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"service": "Service", "industry": "Industry", "news": "News", "media release": "Media Release", "resources": "Resources", "category": "Category"}, "default_value": [], "allow_null": 1, "multiple": 1, "ui": 1, "ajax": 0, "return_format": "value", "placeholder": ""}, {"key": "field_6283c2fc1500f", "label": "Post Type", "name": "post_type", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"post": "Post", "project": "Project", "success_story": "Success Stories", "newsroom": "Newsroom", "more": "More"}, "default_value": false, "allow_null": 0, "multiple": 0, "ui": 0, "return_format": "value", "ajax": 0, "placeholder": ""}], "location": [[{"param": "block", "operator": "==", "value": "acf/posts-section"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1658946665}
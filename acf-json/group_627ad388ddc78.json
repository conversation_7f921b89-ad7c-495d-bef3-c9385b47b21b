{"key": "group_627ad388ddc78", "title": "Block: 3-Column Text & Table", "fields": [{"key": "field_627ad3a14cdc5", "label": "Eyebrow", "name": "eyebrow", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_627ad3b04cdc6", "label": "Title", "name": "title", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 2, "new_lines": "br"}, {"key": "field_627ad3ba4cdc7", "label": "Description", "name": "description", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 4, "new_lines": "br"}, {"key": "field_627ad3c54cdc8", "label": "<PERSON><PERSON>", "name": "button", "aria-label": "", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array"}, {"key": "field_627ad3df4cdc9", "label": "Columns", "name": "columns", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "field_627ad46b4cdca", "min": 0, "max": 0, "layout": "block", "button_label": "Add Column", "sub_fields": [{"key": "field_627ad69950380", "label": "Items", "name": "items", "aria-label": "", "type": "flexible_content", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layouts": {"layout_627ad693cffbd": {"key": "layout_627ad693cffbd", "name": "title", "label": "Title", "display": "block", "sub_fields": [{"key": "field_627ad6a950381", "label": "", "name": "title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_65a57557e5663", "label": "Link", "name": "link", "aria-label": "", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array"}], "min": "", "max": ""}, "layout_627ad6bf50382": {"key": "layout_627ad6bf50382", "name": "content", "label": "Content", "display": "block", "sub_fields": [{"key": "field_627ad6c850383", "label": "", "name": "content", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 2, "new_lines": "br"}, {"key": "field_65a57568e5664", "label": "<PERSON><PERSON>", "name": "button", "aria-label": "", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array"}], "min": "", "max": ""}}, "min": "", "max": "", "button_label": "Add Item", "parent_repeater": "field_627ad3df4cdc9"}], "rows_per_page": 20}], "location": [[{"param": "block", "operator": "==", "value": "acf/text-table-3-columns"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1705342326}
{"key": "group_627c1b255d14d", "title": "Block: Testimonials Carousel", "fields": [{"key": "field_627c1d291018d", "label": "Title", "name": "title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_627c1badfc3f8", "label": "Image Position", "name": "image_position", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "choices": {"left": "Left", "right": "Right"}, "default_value": false, "allow_null": 0, "multiple": 0, "ui": 0, "return_format": "value", "ajax": 0, "placeholder": ""}, {"key": "field_627c1b43fc3f3", "label": "Testimonials", "name": "testimonials", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "field_627c1b5afc3f5", "min": 0, "max": 0, "layout": "block", "button_label": "Add Testimonial", "sub_fields": [{"key": "field_627c1b4dfc3f4", "label": "Quote", "name": "quote", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": 250, "rows": 4, "new_lines": "br"}, {"key": "field_627c1b5afc3f5", "label": "Name", "name": "name", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_627c1b67fc3f6", "label": "Position", "name": "position", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_627c1b81fc3f7", "label": "Image", "name": "image", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_62bc82853ba3e", "label": "Font Size", "name": "font_size", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "choices": {"extra-small": "Extra Small", "small": "Small", "medium": "Medium", "medium-large": "Medium Large", "large": "Large"}, "default_value": "large", "allow_null": 0, "multiple": 0, "ui": 0, "return_format": "value", "ajax": 0, "placeholder": ""}]}], "location": [[{"param": "block", "operator": "==", "value": "acf/testimonials-carousel"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1657308045}
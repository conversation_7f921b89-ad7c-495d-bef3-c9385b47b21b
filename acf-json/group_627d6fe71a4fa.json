{"key": "group_627d6fe71a4fa", "title": "Block: Details Grid", "fields": [{"key": "field_627d7011d4a84", "label": "Eyebrow", "name": "eyebrow", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_627d701cd4a85", "label": "Title", "name": "title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_627d7020d4a86", "label": "Intro", "name": "intro", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 2, "new_lines": "br"}, {"key": "field_627d702bd4a87", "label": "Details", "name": "details", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "field_627d7038d4a88", "min": 0, "max": 0, "layout": "block", "button_label": "Add Detail", "sub_fields": [{"key": "field_627d7038d4a88", "label": "Title", "name": "title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_627d703dd4a89", "label": "Content", "name": "content", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "basic", "media_upload": 0, "delay": 0}, {"key": "field_627d7048d4a8a", "label": "Icon", "name": "icon", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_627d7051d4a8b", "label": "<PERSON><PERSON>", "name": "button", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "return_format": "array"}]}, {"key": "field_627d7069d4a8c", "label": "Details Alignment", "name": "details_alignment", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "34", "class": "", "id": ""}, "choices": {"top": "Top", "center": "Center", "bottom": "Bottom"}, "default_value": false, "allow_null": 0, "multiple": 0, "ui": 0, "return_format": "value", "ajax": 0, "placeholder": ""}, {"key": "field_627d7084d4a8d", "label": "Details per Row", "name": "details_per_row", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33", "class": "", "id": ""}, "choices": {"2": "2", "3": "3"}, "default_value": 3, "allow_null": 0, "multiple": 0, "ui": 0, "return_format": "value", "ajax": 0, "placeholder": ""}, {"key": "field_627d7188b1a5e", "label": "Background", "name": "background", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33", "class": "", "id": ""}, "choices": {"transparent": "Transparent", "blue": "Blue"}, "default_value": "blue", "allow_null": 0, "multiple": 0, "ui": 0, "return_format": "value", "ajax": 0, "placeholder": ""}], "location": [[{"param": "block", "operator": "==", "value": "acf/details-grid"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1656446812}
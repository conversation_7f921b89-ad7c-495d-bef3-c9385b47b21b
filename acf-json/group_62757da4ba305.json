{"key": "group_62757da4ba305", "title": "Theme Options", "fields": [{"key": "field_62757db5d75b4", "label": "Header", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "left", "endpoint": 0}, {"key": "field_62757dbcd75b5", "label": "Logo", "name": "header_logo", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_62853e5deb2b6", "label": "Floating CTA", "name": "floating_cta", "aria-label": "", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array"}, {"key": "field_62757dcbd75b6", "label": "Footer", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "left", "endpoint": 0}, {"key": "field_62757dd4d75b7", "label": "Logo", "name": "footer_logo", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_62757df8d75b8", "label": "Copyright", "name": "copyright", "aria-label": "", "type": "text", "instructions": "Use %year% to display the current year.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_62a9187816216", "label": "Branding", "name": "branding", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Brand & Website by Massive", "tabs": "all", "toolbar": "basic", "media_upload": 0, "delay": 0}, {"key": "field_627a8b9bd5729", "label": "Cookie Disclosure", "name": "cookie_disclosure", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_627a8bacd572a", "label": "Message", "name": "message", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 0}, {"key": "field_627a8bb6d572b", "label": "Button Label", "name": "button_label", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}]}, {"key": "field_62b4f0012ceec", "label": "Bottom Space", "name": "bottom_space", "aria-label": "", "type": "select", "instructions": "Small : 60px\r\nMedium : 160px\r\nLarge : 200px", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"small": "Small", "medium": "Medium", "large": "Large"}, "default_value": "small", "allow_null": 0, "multiple": 0, "ui": 0, "return_format": "value", "ajax": 0, "placeholder": ""}, {"key": "field_6427782f084e9", "label": "Grid Type", "name": "grid_type", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"normal": "normal", "two-rows-three-columns": "two-rows-three-columns"}, "default_value": "normal", "return_format": "value", "multiple": 0, "allow_null": 0, "ui": 0, "ajax": 0, "placeholder": ""}, {"key": "field_627bef3f0afdb", "label": "Settings", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "left", "endpoint": 0}, {"key": "field_627bef460afdc", "label": "Google Maps API Key", "name": "google_maps_api_key", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_6283c055d4c3c", "label": "Projects Archive Page", "name": "projects_archive_page", "aria-label": "", "type": "page_link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "post_type": ["page"], "taxonomy": "", "allow_null": 1, "allow_archives": 0, "multiple": 0}, {"key": "field_62853b696d7b9", "label": "Success Stories Archive Page", "name": "success_stories_archive_page", "aria-label": "", "type": "page_link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "post_type": ["page"], "taxonomy": "", "allow_null": 1, "allow_archives": 0, "multiple": 0}, {"key": "field_62853c8dac7a2", "label": "Team Members Archive Page", "name": "team_members_archive_page", "aria-label": "", "type": "post_object", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "post_type": ["page"], "taxonomy": "", "allow_null": 0, "multiple": 0, "return_format": "id", "ui": 1}, {"key": "field_62be0451b6b2f", "label": "Newsroom Archive Page", "name": "newsroom_archive_page", "aria-label": "", "type": "page_link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "post_type": ["page"], "taxonomy": "", "allow_null": 1, "allow_archives": 0, "multiple": 0}], "location": [[{"param": "options_page", "operator": "==", "value": "theme-general-settings"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1680308469}
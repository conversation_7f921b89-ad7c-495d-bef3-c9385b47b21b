<?php

add_action('acf/init', 'my_acf_init_block_types');
function my_acf_init_block_types() {
    if( function_exists('acf_register_block_type') ) {

        acf_register_block_type(array(
            'name'              => 'primary-hero',
            'title'             => __('Primary Hero', 'mm-smy'),
            'description'       => __('(100.1) Displays an hero image with the page title and description', 'mm-smy'),
            'render_template'   => 'blocks/primary-hero.php',
            'category'          => 'theme-modules',
            'icon'              => 'cover-image',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/primary-hero.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/primary-hero.css',
            'enqueue_script' => get_template_directory_uri() . '/js/blocks/primary-hero.js',
        ));

        acf_register_block_type(array(
            'name'              => 'secondary-hero',
            'title'             => __('Secondary Hero', 'mm-smy'),
            'description'       => __('(100.2) Displays an hero image with a breadcrumb, the page title and description', 'mm-smy'),
            'render_template'   => 'blocks/secondary-hero.php',
            'category'          => 'theme-modules',
            'icon'              => 'cover-image',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/secondary-hero.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/secondary-hero.css',
        ));

        acf_register_block_type(array(
            'name'              => 'styled-background',
            'title'             => __('Styled Background', 'mm-obb'),
            'description'       => __('Change the background color and/or adds a background graphic', 'mm-smy'),
            'render_template'   => 'blocks/styled-background.php',
            'category'          => 'theme-modules',
            'icon'              => 'admin-appearance',
            'mode'              => 'preview',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'jsx' => true,
                'anchor' => true,
            ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/styled-background.css',
        ));

        acf_register_block_type(array(
            'name'              => 'text-table-3-columns',
            'title'             => __('3-Column Text & Table', 'mm-smy'),
            'description'       => __('(33.4) Displays a column with title and description, and two columns with items.', 'mm-smy'),
            'render_template'   => 'blocks/text-table-3-columns.php',
            'category'          => 'theme-modules',
            'icon'              => 'ellipsis',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/text-table-3-columns.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/text-table-3-columns.css',
        ));

        acf_register_block_type(array(
            'name'              => 'content-with-media',
            'title'             => __('Content with Media', 'mm-smy'),
            'description'       => __('(50.4/50.8/50.9) Displays a column with content and another column with an image, video or map.', 'mm-smy'),
            'render_template'   => 'blocks/content-with-media.php',
            'category'          => 'theme-modules',
            'icon'              => 'align-pull-right',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/content-with-media.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/content-with-media.css',
            'enqueue_script' => get_template_directory_uri() . '/js/blocks/content-with-media.js',
        ));

        acf_register_block_type(array(
            'name'              => 'testimonials-carousel',
            'title'             => __('Testimonials Carousel', 'mm-smy'),
            'description'       => __('(50.5) Displays a carousel with testimonials', 'mm-smy'),
            'render_template'   => 'blocks/testimonials-carousel.php',
            'category'          => 'theme-modules',
            'icon'              => 'format-quote',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/testimonials-carousel.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/testimonials-carousel.css',
            'enqueue_script' => get_template_directory_uri() . '/js/blocks/testimonials-carousel.js',
        ));

        acf_register_block_type(array(
            'name'              => 'posts-cards',
            'title'             => __('Posts cards', 'mm-smy'),
            'description'       => __('(33.1) Displays selected posts or the most recent ones.', 'mm-smy'),
            'render_template'   => 'blocks/posts-cards.php',
            'category'          => 'theme-modules',
            'icon'              => 'admin-post',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/posts-cards.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/posts-cards.css',
        ));

        acf_register_block_type(array(
            'name'              => 'content-cards',
            'title'             => __('Content Cards', 'mm-smy'),
            'description'       => __('Displays custom contents or external links.', 'mm-smy'),
            'render_template'   => 'blocks/content-cards.php',
            'category'          => 'theme-modules',
            'icon'              => 'admin-post',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/posts-cards.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/content-cards.css',
        ));

        acf_register_block_type(array(
            'name'              => 'call-to-action',
            'title'             => __('Call to Action', 'mm-smy'),
            'description'       => __('(100.3) Displays a call to action with eyebrow, headline and button.', 'mm-smy'),
            'render_template'   => 'blocks/call-to-action.php',
            'category'          => 'theme-modules',
            'icon'              => 'external',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/call-to-action.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/call-to-action.css',
        ));

        acf_register_block_type(array(
            'name'              => 'side-by-side-content',
            'title'             => __('Side by Side Content', 'mm-smy'),
            'description'       => __('Displays two columns with content and editable options.', 'mm-smy'),
            'render_template'   => 'blocks/side-by-side-content.php',
            'category'          => 'theme-modules',
            'icon'              => 'columns',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/side-by-side-content.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/side-by-side-content.css',
        ));

        acf_register_block_type(array(
            'name'              => 'text-2-columns',
            'title'             => __('2-Column Text', 'mm-smy'),
            'description'       => __('(50.3) Displays two columns with content.', 'mm-smy'),
            'render_template'   => 'blocks/text-2-columns.php',
            'category'          => 'theme-modules',
            'icon'              => 'controls-pause',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/text-2-columns.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/text-2-columns.css',
        ));

        acf_register_block_type(array(
            'name'              => 'cards',
            'title'             => __('Cards', 'mm-smy'),
            'description'       => __('(50.6) Displays cards with title and content.', 'mm-smy'),
            'render_template'   => 'blocks/cards.php',
            'category'          => 'theme-modules',
            'icon'              => 'screenoptions',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/cards.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/cards.css',
        ));

        acf_register_block_type(array(
            'name'              => 'location-cards',
            'title'             => __('Location Cards', 'mm-smy'),
            'description'       => __('(33.3) Displays location cards with title and content.', 'mm-smy'),
            'render_template'   => 'blocks/location-cards.php',
            'category'          => 'theme-modules',
            'icon'              => 'screenoptions',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/location-cards.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/location-cards.css',
        ));

        acf_register_block_type(array(
            'name'              => 'details-grid',
            'title'             => __('Details Grid', 'mm-smy'),
            'description'       => __('(50.6) Displays eyebrow, title, description and a grid with detailed content.', 'mm-smy'),
            'render_template'   => 'blocks/details-grid.php',
            'category'          => 'theme-modules',
            'icon'              => 'media-spreadsheet',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/details-grid.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/details-grid.css',
        ));

        acf_register_block_type(array(
            'name'              => 'posts-section',
            'title'             => __('Posts', 'mm-smy'),
            'description'       => __('Displays posts', 'mm-smy'),
            'render_template'   => 'blocks/posts-section.php',
            'category'          => 'theme-modules',
            'icon'              => 'admin-post',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/posts-section.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/posts-section.css',
            'enqueue_script' => get_template_directory_uri() . '/js/blocks/posts-section.js',
        ));

        acf_register_block_type(array(
            'name'              => 'brands-carousel',
            'title'             => __('Brands Carousel', 'mm-smy'),
            'description'       => __('Displays a carousel of brands, optionally with links', 'mm-smy'),
            'render_template'   => 'blocks/brands-carousel.php',
            'category'          => 'theme-modules',
            'icon'              => 'slides',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/brands-carousel.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/brands-carousel.css',
            'enqueue_script' => get_template_directory_uri() . '/js/blocks/brands-carousel.js',
        ));

        acf_register_block_type(array(
            'name'              => 'text-accordion-2-columns',
            'title'             => __('2-Column Text Accordion', 'mm-smy'),
            'description'       => __('(50.1) Displays two columns, one with content and the other with accordions.', 'mm-smy'),
            'render_template'   => 'blocks/text-accordion-2-columns.php',
            'category'          => 'theme-modules',
            'icon'              => 'editor-kitchensink',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/text-accordion-2-columns.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/text-accordion-2-columns.css',
            'enqueue_script' => get_template_directory_uri() . '/js/blocks/text-accordion-2-columns.js',
        ));

        acf_register_block_type(array(
            'name'              => 'team-carousel',
            'title'             => __('Team Members Carousel', 'mm-smy'),
            'description'       => __('Displays a carousel of team members', 'mm-smy'),
            'render_template'   => 'blocks/team-carousel.php',
            'category'          => 'theme-modules',
            'icon'              => 'slides',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/team-carousel.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/team-carousel.css',
            'enqueue_script' => get_template_directory_uri() . '/js/blocks/team-carousel.js',
        ));

        acf_register_block_type(array(
            'name'              => 'related-posts',
            'title'             => __('Related posts', 'mm-smy'),
            'description'       => __('(33.1) Displays content related to the current one.', 'mm-smy'),
            'render_template'   => 'blocks/related-posts.php',
            'category'          => 'theme-modules',
            'icon'              => 'admin-post',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/related-posts.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/related-posts.css',
        ));

        acf_register_block_type(array(
            'name'              => 'user-posts',
            'title'             => __('User posts', 'mm-smy'),
            'description'       => __('(33.1) Displays content related to a user.', 'mm-smy'),
            'render_template'   => 'blocks/user-posts.php',
            'category'          => 'theme-modules',
            'icon'              => 'admin-post',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/user-posts.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/user-posts.css',
        ));

        acf_register_block_type(array(
            'name'              => 'team-members',
            'title'             => __('Team members', 'mm-smy'),
            'description'       => __('(33.6) Displays the team members.', 'mm-smy'),
            'render_template'   => 'blocks/team-members.php',
            'category'          => 'theme-modules',
            'icon'              => 'businessman',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/team-members.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/team-members.css',
            'enqueue_script' => get_template_directory_uri() . '/js/blocks/team-members.js',
        ));

        acf_register_block_type(array(
            'name'              => 'text-3-columns',
            'title'             => __('3-Column Text', 'mm-smy'),
            'description'       => __('(33.5) Displays an eyebrow, title and 3 columns of text.', 'mm-smy'),
            'render_template'   => 'blocks/text-3-columns.php',
            'category'          => 'theme-modules',
            'icon'              => 'schedule',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/text-3-columns.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/text-3-columns.css',
        ));

        acf_register_block_type(array(
            'name'              => 'form-cta',
            'title'             => __('Call To Action With Form', 'mm-smy'),
            'description'       => __('(100.4) Displays a call to action with form', 'mm-smy'),
            'render_template'   => 'blocks/form-cta.php',
            'category'          => 'theme-modules',
            'icon'              => 'share-alt2',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/form-cta.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/form-cta.css',
        ));

        acf_register_block_type(array(
            'name'              => 'tabs',
            'title'             => __('Tabs', 'mm-smy'),
            'description'       => __('(50.2) Displays content in tabs.', 'mm-smy'),
            'render_template'   => 'blocks/tabs.php',
            'category'          => 'theme-modules',
            'icon'              => 'align-pull-right',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/tabs.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/tabs.css',
            'enqueue_script' => get_template_directory_uri() . '/js/blocks/tabs.js',
        ));

        acf_register_block_type(array(
            'name'              => 'page-navigation',
            'title'             => __('Page Navigation', 'mm-smy'),
            'description'       => __('A navigation bar to links in the same page', 'mm-smy'),
            'render_template'   => 'blocks/page-navigation.php',
            'category'          => 'theme-modules',
            'icon'              => 'minus',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'render_callback'   => 'theme_acf_block_render_callback',
            'example'  => array(
	            'attributes' => array(
	                'mode' => 'preview',
	                'data' => array(
	                	'preview_image' => '/img/blocks/page-navigation.png',
	                )
	            )
	        ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/page-navigation.css',
        ));

        acf_register_block_type(array(
            'name'              => 'debt-calculator',
            'title'             => __('Debt Calculator', 'mm-smy'),
            'description'       => __('Displays a debt calculator.', 'mm-smy'),
            'render_template'   => 'blocks/debt-calculator.php',
            'category'          => 'theme-modules',
            'icon'              => 'calculator',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/debt-calculator.css',
            'enqueue_script' => get_template_directory_uri() . '/js/blocks/debt-calculator.js',
        ));

        acf_register_block_type(array(
            'name'              => 'social-media',
            'title'             => __('Social Media', 'mm-smy'),
            'description'       => __('Displays a list of social media.', 'mm-smy'),
            'render_template'   => 'blocks/social-media.php',
            'category'          => 'theme-modules',
            'icon'              => 'networking',
            'mode'              => 'edit',
            'supports'          => array(
                'align' => false,
                'mode' => false,
                'anchor' => true,
            ),
            'enqueue_style' => get_template_directory_uri() . '/css/blocks/social-media.css',
        ));

    }
}


// Blocks helper
function theme_block_class($block, $classes){
    if( !empty($block['className']) ) {
        $classes .= ' ' . $block['className'];
    }
    if( !empty($block['align']) ) {
        $classes .= ' align' . $block['align'];
    }
    return $classes;
}

function theme_block_id($block){
    if( !empty($block['anchor']) ) {
        return $block['anchor'];
    }else{
        return $block['id'];
    }
}

function theme_acf_block_render_callback( $block, $content = '', $is_preview = false, $post_id = 0 ) {
	if( isset( $block['data']['preview_image'] ) ){
        echo '<img src="'.get_stylesheet_directory_uri() . $block['data']['preview_image'].'" alt="'.$block['title1'].'" style="max-width: 100%;">';
    }else{
        if ( file_exists( $block['render_template'] ) ) {
            $path = $block['render_template'];
        } else {
            $path = locate_template( $block['render_template'] );
        }

        if ( file_exists( $path ) ) {
            include $path;
        }
    }
}
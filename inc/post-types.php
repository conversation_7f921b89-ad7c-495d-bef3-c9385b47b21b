<?php

function cptui_register_my_cpts() {

	/**
	 * Post Type: Projects.
	 */

	$labels = [
		"name" => __( "Projects", "mm-smy" ),
		"singular_name" => __( "Project", "mm-smy" ),
	];

	$args = [
		"label" => __( "Projects", "mm-smy" ),
		"labels" => $labels,
		"description" => "",
		"public" => true,
		"publicly_queryable" => true,
		"show_ui" => true,
		"show_in_rest" => true,
		"rest_base" => "",
		"rest_controller_class" => "WP_REST_Posts_Controller",
		"rest_namespace" => "wp/v2",
		"has_archive" => false,
		"show_in_menu" => true,
		"show_in_nav_menus" => true,
		"delete_with_user" => false,
		"exclude_from_search" => true,
		"capability_type" => "post",
		"map_meta_cap" => true,
		"hierarchical" => false,
		"can_export" => true,
		"rewrite" => [ "slug" => "project", "with_front" => false ],
		"query_var" => true,
		"menu_icon" => "dashicons-format-aside",
		"supports" => [ "title", "editor", "thumbnail" ],
		"show_in_graphql" => false,
	];

	register_post_type( "project", $args );

	/**
	 * Post Type: Newsroom.
	 */

	$labels = [
		"name" => __( "Newsroom", "mm-smy" ),
		"singular_name" => __( "Newsroom", "mm-smy" ),
	];

	$args = [
		"label" => __( "Newsroom", "mm-smy" ),
		"labels" => $labels,
		"description" => "",
		"public" => true,
		"publicly_queryable" => true,
		"show_ui" => true,
		"show_in_rest" => true,
		"rest_base" => "",
		"rest_controller_class" => "WP_REST_Posts_Controller",
		"rest_namespace" => "wp/v2",
		"has_archive" => true,
		"show_in_menu" => true,
		"show_in_nav_menus" => true,
		"delete_with_user" => false,
		"exclude_from_search" => false,
		"capability_type" => "post",
		"map_meta_cap" => true,
		"hierarchical" => false,
		"can_export" => true,
		"rewrite" => [ "slug" => "newsroom", "with_front" => false ],
		"query_var" => true,
		"menu_icon" => "dashicons-testimonial",
		"supports" => [ "title", "editor", "thumbnail" ],
		"show_in_graphql" => false,
	];

	register_post_type( "newsroom", $args );

	/**
	 * Post Type: More.
	 */

	$labels = [
		"name" => __( "More", "mm-smy" ),
		"singular_name" => __( "More", "mm-smy" ),
	];

	$args = [
		"label" => __( "More", "mm-smy" ),
		"labels" => $labels,
		"description" => "",
		"public" => true,
		"publicly_queryable" => true,
		"show_ui" => true,
		"show_in_rest" => true,
		"rest_base" => "",
		"rest_controller_class" => "WP_REST_Posts_Controller",
		"rest_namespace" => "wp/v2",
		"has_archive" => true,
		"show_in_menu" => true,
		"show_in_nav_menus" => true,
		"delete_with_user" => false,
		"exclude_from_search" => false,
		"capability_type" => "post",
		"map_meta_cap" => true,
		"hierarchical" => false,
		"can_export" => true,
		"rewrite" => [ "slug" => "more-list", "with_front" => false ],
		"query_var" => true,
		"menu_icon" => "dashicons-list-view",
		"supports" => [ "title", "editor", "thumbnail" ],
		"show_in_graphql" => false,
		"taxonomies" => [ "category" ],
	];

	register_post_type( "more", $args );

	/**
	 * Post Type: Team Members.
	 */

	$labels = [
		"name" => __( "Team Members", "mm-smy" ),
		"singular_name" => __( "Team Member", "mm-smy" ),
	];

	$args = [
		"label" => __( "Team Members", "mm-smy" ),
		"labels" => $labels,
		"description" => "",
		"public" => true,
		"publicly_queryable" => true,
		"show_ui" => true,
		"show_in_rest" => true,
		"rest_base" => "",
		"rest_controller_class" => "WP_REST_Posts_Controller",
		"rest_namespace" => "wp/v2",
		"has_archive" => false,
		"show_in_menu" => true,
		"show_in_nav_menus" => false,
		"delete_with_user" => false,
		"exclude_from_search" => true,
		"capability_type" => "post",
		"map_meta_cap" => true,
		"hierarchical" => false,
		"can_export" => true,
		"rewrite" => [ "slug" => "team-member", "with_front" => false ],
		"query_var" => true,
		"menu_icon" => "dashicons-businessman",
		"supports" => [ "title", "editor", "thumbnail" ],
		"show_in_graphql" => false,
	];

	register_post_type( "team_member", $args );

	/**
	 * Post Type: Success Stories.
	 */

	$labels = [
		"name" => __( "Success Stories", "mm-smy" ),
		"singular_name" => __( "Success Story", "mm-smy" ),
	];

	$args = [
		"label" => __( "Success Stories", "mm-smy" ),
		"labels" => $labels,
		"description" => "",
		"public" => true,
		"publicly_queryable" => true,
		"show_ui" => true,
		"show_in_rest" => true,
		"rest_base" => "",
		"rest_controller_class" => "WP_REST_Posts_Controller",
		"rest_namespace" => "wp/v2",
		"has_archive" => true,
		"show_in_menu" => true,
		"show_in_nav_menus" => true,
		"delete_with_user" => false,
		"exclude_from_search" => false,
		"capability_type" => "post",
		"map_meta_cap" => true,
		"hierarchical" => false,
		"can_export" => true,
		"rewrite" => [ "slug" => "success_story", "with_front" => false ],
		"query_var" => true,
		"menu_icon" => "dashicons-awards",
		"supports" => [ "title", "editor", "thumbnail" ],
		"taxonomies" => [ "service" ],
		"show_in_graphql" => false,
	];

	register_post_type( "success_story", $args );
}

add_action( 'init', 'cptui_register_my_cpts' );


function cptui_register_my_taxes() {

	/**
		* Taxonomy: Services.
		*/

	$labels = [
		"name" => __( "Services", "mm-smy" ),
		"singular_name" => __( "Service", "mm-smy" ),
	];


	$args = [
		"label" => __( "Services", "mm-smy" ),
		"labels" => $labels,
		"public" => true,
		"publicly_queryable" => true,
		"hierarchical" => true,
		"show_ui" => true,
		"show_in_menu" => true,
		"show_in_nav_menus" => false,
		"query_var" => true,
		"rewrite" => [ 'slug' => 'service', 'with_front' => false, ],
		"show_admin_column" => true,
		"show_in_rest" => true,
		"show_tagcloud" => false,
		"rest_base" => "service",
		"rest_controller_class" => "WP_REST_Terms_Controller",
		"rest_namespace" => "wp/v2",
		"show_in_quick_edit" => true,
		"sort" => false,
		"show_in_graphql" => false,
	];
	register_taxonomy( "service", [ "post", "project" ], $args );

	/**
		* Taxonomy: Industries.
		*/

	$labels = [
		"name" => __( "Industries", "mm-smy" ),
		"singular_name" => __( "Industry", "mm-smy" ),
	];


	$args = [
		"label" => __( "Industries", "mm-smy" ),
		"labels" => $labels,
		"public" => true,
		"publicly_queryable" => true,
		"hierarchical" => true,
		"show_ui" => true,
		"show_in_menu" => true,
		"show_in_nav_menus" => false,
		"query_var" => true,
		"rewrite" => [ 'slug' => 'industry', 'with_front' => false, ],
		"show_admin_column" => true,
		"show_in_rest" => true,
		"show_tagcloud" => false,
		"rest_base" => "industry",
		"rest_controller_class" => "WP_REST_Terms_Controller",
		"rest_namespace" => "wp/v2",
		"show_in_quick_edit" => true,
		"sort" => false,
		"show_in_graphql" => false,
	];
	register_taxonomy( "industry", [ "post", "project" ], $args );

	/**
		* Taxonomy: News Types.
		*/

	$labels = [
		"name" => __( "News", "mm-smy" ),
		"singular_name" => __( "News", "mm-smy" ),
	];


	$args = [
		"label" => __( "News", "mm-smy" ),
		"labels" => $labels,
		"public" => true,
		"publicly_queryable" => true,
		"hierarchical" => true,
		"show_ui" => true,
		"show_in_menu" => true,
		"show_in_nav_menus" => false,
		"query_var" => true,
		"rewrite" => [ 'slug' => 'news', 'with_front' => false, ],
		"show_admin_column" => true,
		"show_in_rest" => true,
		"show_tagcloud" => false,
		"rest_base" => "news",
		"rest_controller_class" => "WP_REST_Terms_Controller",
		"rest_namespace" => "wp/v2",
		"show_in_quick_edit" => true,
		"sort" => false,
		"show_in_graphql" => false,
	];
	register_taxonomy( "news", [ "newsroom" ], $args );

	/**
		* Taxonomy: Resources.
		*/

	$labels = [
		"name" => __( "Resources", "mm-smy" ),
		"singular_name" => __( "Resource", "mm-smy" ),
	];


	$args = [
		"label" => __( "Resources", "mm-smy" ),
		"labels" => $labels,
		"public" => true,
		"publicly_queryable" => true,
		"hierarchical" => true,
		"show_ui" => true,
		"show_in_menu" => true,
		"show_in_nav_menus" => false,
		"query_var" => true,
		"rewrite" => [ 'slug' => 'resources', 'with_front' => false, ],
		"show_admin_column" => true,
		"show_in_rest" => true,
		"show_tagcloud" => false,
		"rest_base" => "resources",
		"rest_controller_class" => "WP_REST_Terms_Controller",
		"rest_namespace" => "wp/v2",
		"show_in_quick_edit" => true,
		"sort" => false,
		"show_in_graphql" => false,
	];
	register_taxonomy( "resources", [ "more" ], $args );

	/**
	* Taxonomy: Positions.
	*/

	$labels = [
		"name" => __( "Positions", "mm-smy" ),
		"singular_name" => __( "Position", "mm-smy" ),
	];


	$args = [
		"label" => __( "Positions", "mm-smy" ),
		"labels" => $labels,
		"public" => true,
		"publicly_queryable" => true,
		"hierarchical" => true,
		"show_ui" => true,
		"show_in_menu" => true,
		"show_in_nav_menus" => false,
		"query_var" => true,
		"rewrite" => [ 'slug' => 'position', 'with_front' => false, ],
		"show_admin_column" => true,
		"show_in_rest" => true,
		"show_tagcloud" => false,
		"rest_base" => "position",
		"rest_controller_class" => "WP_REST_Terms_Controller",
		"rest_namespace" => "wp/v2",
		"show_in_quick_edit" => true,
		"sort" => false,
		"show_in_graphql" => false,
	];
	register_taxonomy( "position", [ "team_member" ], $args );

	/**
	* Taxonomy: Services.
	*/

	$labels = [
		"name" => __( "Services", "mm-smy" ),
		"singular_name" => __( "Service", "mm-smy" ),
	];


	$args = [
		"label" => __( "Services", "mm-smy" ),
		"labels" => $labels,
		"public" => true,
		"publicly_queryable" => true,
		"hierarchical" => true,
		"show_ui" => true,
		"show_in_menu" => true,
		"show_in_nav_menus" => false,
		"query_var" => true,
		"rewrite" => [ 'slug' => 'service', 'with_front' => false, ],
		"show_admin_column" => true,
		"show_in_rest" => true,
		"show_tagcloud" => false,
		"rest_base" => "service",
		"rest_controller_class" => "WP_REST_Terms_Controller",
		"rest_namespace" => "wp/v2",
		"show_in_quick_edit" => true,
		"sort" => false,
		"show_in_graphql" => false,
	];
	register_taxonomy( "service", [ "team_member" ], $args );

	/**
	* Taxonomy: Locations.
	*/

	$labels = [
		"name" => __( "Locations", "mm-smy" ),
		"singular_name" => __( "Location", "mm-smy" ),
	];


	$args = [
		"label" => __( "Locations", "mm-smy" ),
		"labels" => $labels,
		"public" => true,
		"publicly_queryable" => true,
		"hierarchical" => true,
		"show_ui" => true,
		"show_in_menu" => true,
		"show_in_nav_menus" => false,
		"query_var" => true,
		"rewrite" => [ 'slug' => 'location', 'with_front' => false, ],
		"show_admin_column" => true,
		"show_in_rest" => true,
		"show_tagcloud" => false,
		"rest_base" => "experience",
		"rest_controller_class" => "WP_REST_Terms_Controller",
		"rest_namespace" => "wp/v2",
		"show_in_quick_edit" => true,
		"sort" => false,
		"show_in_graphql" => false,
	];
	register_taxonomy( "location", [ "team_member" ], $args );

}
add_action( 'init', 'cptui_register_my_taxes' );


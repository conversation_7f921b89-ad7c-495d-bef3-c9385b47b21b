<?php

require_once get_template_directory() . '/inc/class-tgm-plugin-activation.php';
add_action( 'tgmpa_register', 'theme_register_required_plugins' );

function theme_register_required_plugins() {
    $plugins = array(
		array(
			'name'               => 'Advanced Custom Fields PRO',
			'slug'               => 'advanced-custom-fields-pro',
			'source'             => get_template_directory() . '/plugins/advanced-custom-fields-pro.zip',
			'required'           => true,
			'version'            => '5.11.4',
			'force_activation'   => true,
			'is_callable'        => 'add_theme_field_groups',
		),
		array(
			'name'               => 'Gravity Forms',
			'slug'               => 'gravityforms',
			'source'             => get_template_directory() . '/plugins/gravityforms.zip',
		),
        array(
			'name'        		 => 'WordPress SEO by Yoast',
			'slug'        		 => 'wordpress-seo',
			'is_callable' 		 => 'wpseo_init',
		),
        array(
			'name'        		 => 'Safe SVG',
			'slug'        		 => 'safe-svg',
		),
        array(
			'name'        		 => 'Insert Headers and Footers',
			'slug'        		 => 'insert-headers-and-footers',
		),
        array(
			'name'        		 => 'Duplicate Page',
			'slug'        		 => 'duplicate-page',
		),
        array(
			'name'        		 => 'Autoptimize',
			'slug'        		 => 'autoptimize',
		),
    );

	$config = array(
		'id'           => 'mm-dv',
		'default_path' => '',
		'menu'         => 'tgmpa-install-plugins',
		'parent_slug'  => 'themes.php',
		'capability'   => 'edit_theme_options',
		'has_notices'  => true,
		'dismissable'  => true,
		'dismiss_msg'  => '',
		'is_automatic' => false,
		'message'      => '',
    );
    
	tgmpa( $plugins, $config );
}




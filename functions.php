<?php

// Include helpers
require_once 'inc/plugins.php';
require_once 'inc/post-types.php';
require_once 'inc/blocks.php';

// Include styles
function theme_enqueue_styles(){
    wp_enqueue_style( 'font-opensans', 'https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,400;0,600;1,400;1,600&display=swap', false);
    wp_enqueue_style( 'font-montserrat', 'https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,400;0,500;0,600;0,800;1,400;1,500;1,600;1,800&display=swap', false);
    wp_enqueue_style( 'swiper-css', get_template_directory_uri() . '/vendor/swiper/swiper-bundle.min.css', false);
    wp_enqueue_style( 'base', get_template_directory_uri() . '/css/base.css', false);
    wp_enqueue_style( 'style', get_template_directory_uri() . '/style.css', false);

}
add_action( 'wp_enqueue_scripts', 'theme_enqueue_styles');
function theme_enqueue_gutenberg_assets(){
    wp_enqueue_style( 'gutenberg', get_template_directory_uri() . '/css/gutenberg.css', false);
    wp_enqueue_script( 'gutenberg-js', get_template_directory_uri() . '/js/gutenberg.js', array('jquery'), '1.0', true );
}
add_action( 'enqueue_block_editor_assets', 'theme_enqueue_gutenberg_assets' );

function theme_enqueue_admin_assets(){
    wp_enqueue_style( 'theme-admin-css', get_template_directory_uri() . '/css/admin.css', false);
    wp_enqueue_script( 'theme-admin-js', get_template_directory_uri() . '/js/admin.js', array('jquery'), '1.0', true );
}
add_action('admin_enqueue_scripts', 'theme_enqueue_admin_assets');

// Include scripts
function theme_enqueue_scripts() {
    wp_enqueue_script( 'swiper-js', get_template_directory_uri() . '/vendor/swiper/swiper-bundle.min.js', '1.0', true );
    wp_enqueue_script( 'main-js', get_template_directory_uri() . '/js/main.js', array('jquery'), '1.0', true );
    $vars = array(
        'site_url' => esc_url(get_home_url()),
        'ajax_url' => admin_url('admin-ajax.php'),
        'theme_url' => get_template_directory_uri(),
    );
    wp_localize_script('main-js', 'theme', $vars);
}
add_action( 'wp_enqueue_scripts', 'theme_enqueue_scripts' );


// Get menu itens for template
function theme_get_menu_items($menu_name){
    if ( ( $locations = get_nav_menu_locations() ) && isset( $locations[ $menu_name ] ) ) {
        $menu = wp_get_nav_menu_object( $locations[ $menu_name ] );
        return wp_get_nav_menu_items($menu->term_id);
    }
}

function theme_menu($menu_name, $depth = 2){
    wp_nav_menu( array(
        'theme_location'    => $menu_name,
        'depth'             => $depth,
        'container'         => 'div',
        'container_id'      => $menu_name,
        )
    );
}


// Include asset
function theme_asset($path){
    echo file_get_contents($path, true);
}


// Set ACF Google Maps API
function theme_acf_google_maps() {
    acf_update_setting('google_api_key', get_field('google_maps_api_key', 'option'));
}
add_action('acf/init', 'theme_acf_google_maps');


// Theme Init
function theme_init(){
    // Add title support
    add_theme_support( 'title-tag' );

    // Add support for wide images in blog posts
    add_theme_support( 'align-wide' );

    // Add support for page excerpt
    add_post_type_support( 'page', 'excerpt' );

    // Theme Settings
    if( function_exists('acf_add_options_page') ) {
        acf_add_options_page(array(
            'page_title' 	=> 'Theme Options',
            'menu_title'	=> 'Theme Options',
            'menu_slug' 	=> 'theme-general-settings',
            'capability'	=> 'edit_posts',
            'redirect'		=> false
        ));
    }

    // Register menu
    register_nav_menus( array(
        'topbar_menu' => 'Top Bar Menu',
        'main_menu' => 'Main Menu',
        'copyright_menu' => 'Copyright Menu',
    ) );

    // Sidebar
    register_sidebar(array(
        'name' => "Footer - Column 1",
        'id' => 'footer-widgets',
        'description' => '',
        'before_widget' => '',
        'after_widget' => '',
        'before_title' => '<h2>',
        'after_title' => '</h2>',
    ));
    register_sidebar(array(
        'name' => "Footer - Column 2",
        'id' => 'footer-widgets-2',
        'description' => '',
        'before_widget' => '',
        'after_widget' => '',
        'before_title' => '<h2>',
        'after_title' => '</h2>',
    ));
    register_sidebar(array(
        'name' => "Footer - Column 3",
        'id' => 'footer-widgets-3',
        'description' => '',
        'before_widget' => '',
        'after_widget' => '',
        'before_title' => '<h2>',
        'after_title' => '</h2>',
    ));
    register_sidebar(array(
        'name' => "Footer - Column 4",
        'id' => 'footer-widgets-4',
        'description' => '',
        'before_widget' => '',
        'after_widget' => '',
        'before_title' => '<h2>',
        'after_title' => '</h2>',
    ));
    register_sidebar(array(
        'name' => "Posts Widgets",
        'id' => 'posts-widgets',
        'description' => '',
        'before_widget' => '',
        'after_widget' => '',
        'before_title' => '<h2>',
        'after_title' => '</h2>',
    ));
    register_sidebar(array(
        'name' => "Projects Widgets",
        'id' => 'projects-widgets',
        'description' => '',
        'before_widget' => '',
        'after_widget' => '',
        'before_title' => '<h2>',
        'after_title' => '</h2>',
    ));
    register_sidebar(array(
        'name' => "Success Stories Widgets",
        'id' => 'success-stories-widgets',
        'description' => '',
        'before_widget' => '',
        'after_widget' => '',
        'before_title' => '<h2>',
        'after_title' => '</h2>',
    ));
    register_sidebar(array(
        'name' => "Team Members Widgets",
        'id' => 'team-members-widgets',
        'description' => '',
        'before_widget' => '',
        'after_widget' => '',
        'before_title' => '<h2>',
        'after_title' => '</h2>',
    ));

    // Thumbnails
    add_theme_support( 'post-thumbnails' );
    set_post_thumbnail_size( 824, 610, true );
    //add_image_size('team-thumb', 640, 960, true);

    // Creates a category for the theme blocks
    function theme_block_categories( $categories ) {
        return array_merge(
            [
                [
                    'slug'  => 'theme-modules',
                    'title' => __( 'Theme Modules', 'mm-smy' ),
                ],
            ],
            $categories
        );
    }
    add_action( 'block_categories_all', 'theme_block_categories', 10, 2 );

    // Add toggle for mobile navigation
    function theme_add_mobile_submenu_toggle( $item_output, $item, $depth, $args ) {

        if ( $args->theme_location == 'main_menu' ) {

            if ( in_array( 'menu-item-has-children', $item->classes ) || in_array( 'page_item_has_children', $item->classes ) ) {
                $item_output = str_replace( $args->link_after . '</a>', $args->link_after . '</a><button class="sub-menu-toggle"><span class="sr-only">' . __( 'Toggle submenu', 'mm-smy' ) . '</span></button>', $item_output );
            }
        }

        return $item_output;
    }
    add_filter( 'walker_nav_menu_start_el', 'theme_add_mobile_submenu_toggle', 10, 4 );

    // Add submenu class to menu
    function theme_add_submenu_class( $items, $args ) {
        if( $args -> theme_location == "main_menu" ) {
            foreach($items as &$item){
                $submenu_style = get_field('submenu_style', $item);
                if(!empty($submenu_style)){
                    array_push($item->classes, $submenu_style);
                }
            }
        }

        return $items;
    }
    add_filter( 'wp_nav_menu_objects', 'theme_add_submenu_class', 10, 2 );

    // Add widget marker to submenu
    function theme_add_widget_market( $items, $args ) {
        if( $args -> theme_location == "main_menu" ) {
            foreach($items as $item){
                if(in_array('submenu-widget', $item->classes)){
                    array_push($items, (object) array(
                        'title' => '[submenu_widget]',
                        'menu_item_parent' => $item->ID,
                        'ID' => 'widget',
                        'url' => '',
                        'classes' => array(),
                    ));
                }
            }
        }

        return $items;
    }
    add_filter( 'wp_nav_menu_objects', 'theme_add_widget_market', 10, 2 );

    // Add toggle for mobile navigation
    function theme_add_menu_widgets( $item_output, $item, $depth, $args ) {
        if ( $args->theme_location == 'main_menu' ) {
            if($item->title == '[submenu_widget]'){
                $item_output = load_template_part('part/menu-widget', null, array('item' => $item->menu_item_parent));
            }
        }

        return $item_output;
    }
    add_filter( 'walker_nav_menu_start_el', 'theme_add_menu_widgets', 10, 4 );

    // Get template content
    function load_template_part($template_name, $part_name = null, $args = array()) {
        ob_start();
        get_template_part($template_name, $part_name, $args);
        $template = ob_get_contents();
        ob_end_clean();
        return $template;
    }

    // CPT Archive Redirect
    function theme_archive_redirect() {
        if( is_post_type_archive('project') ) {
            if(get_field('projects_archive_page', 'option')){
                wp_redirect( get_field('projects_archive_page', 'option'), 301 );
                exit();
            }
        }else if( is_post_type_archive('success_story') ) {
            if(get_field('success_stories_archive_page', 'option')){
                wp_redirect( get_field('success_stories_archive_page', 'option'), 301 );
                exit();
            }
        }else if( is_post_type_archive('newsroom') ) {
            if(get_field('newsroom_archive_page', 'option')){
                wp_redirect( get_field('newsroom_archive_page', 'option'), 301 );
                exit();
            }
        }else if( is_post_type_archive('more') ) {
            if(get_field('additional_resources_archive_page', 'option')){
                wp_redirect( get_field('additional_resources_archive_page', 'option'), 301 );
                exit();
            }
        }
    }
    add_action( 'template_redirect', 'theme_archive_redirect' );

}
add_action( 'init', 'theme_init' );


// Change Gravity Forms submit to button
add_filter( 'gform_submit_button', 'theme_form_submit_button', 10, 2 );
function theme_form_submit_button( $button, $form ) {
    $dom = new DOMDocument();
    $dom->loadHTML( '<?xml encoding="utf-8" ?>' . $button );
    $input = $dom->getElementsByTagName( 'input' )->item(0);
    $label = $input->getAttribute( 'value' );

    return "<button class='button gform_button button-light-filled submit-button' id='gform_submit_button_{$form['id']}'><span>$label</span></button>";
}

// Disable automatic scroll on forms
add_filter( 'gform_confirmation_anchor', '__return_false' );


// Toggle query attribute
function toggle_query_arg($arg, $value){
    $selected_args = explode(',', $_GET[$arg]);

    if (($key = array_search($value, $selected_args)) !== false) {
        unset($selected_args[$key]);
        if(empty($selected_args)) return remove_query_arg($arg);
    }else{
        array_push($selected_args, $value);
    }

    $arg_string = implode(',', array_filter($selected_args));
    return add_query_arg(array($arg => $arg_string, 'paged' => 1));
}

// Search Results Pagination
function search_filter($query) {
  if ( !is_admin() && $query->is_main_query() ) {
    if ($query->is_search) {
      $query->set('paged', ( get_query_var('paged') ) ? get_query_var('paged') : 1 );
      $query->set('posts_per_page', 9);
    }
  }
}
add_action( 'pre_get_posts', 'search_filter' );

// Restrict allowed blocks in team members
//function theme_allowed_blocks_team($allowed_block_types, $post) {
//    if($post->post_type == 'team_member') {
//        return array(
//            'core/paragraph',
//        );
//    } else {
//        return $allowed_block_types;
//    }
//}
//add_filter('allowed_block_types', 'theme_allowed_blocks_team', 10, 2);

// Filter to allow Iframes on ACF with its corresponding attributes
add_filter( 'wp_kses_allowed_html', 'acf_add_allowed_iframe_tag', 10, 2 );
function acf_add_allowed_iframe_tag( $tags, $context ) {
    if ( $context === 'acf' ) {
        $tags['iframe'] = array(
            'title'           => true,
            'src'             => true,
            'height'          => true,
            'width'           => true,
            'frameborder'     => true,
            'allow'           => true,
            'allowfullscreen' => true,
            'style'           => true,
            'loading'         => true,
            'referrerpolicy'  => true
        );
    }

    return $tags;
}

add_action('rest_api_init', function() {
    register_rest_route('your-endpoint/v1', '/get-email', [
        'methods' => 'POST',
        'callback' => 'get_team_member_email',
        'permission_callback' => '__return_true'
    ]);
});

function get_team_member_email($request) {
    $headers = getallheaders();
    if (!isset($headers['Custom-Token']) || $headers['Custom-Token'] !== CUSTOM_API_TOKEN) {
        return new WP_Error('forbidden', 'Access denied', array('status' => 403));
    }

    $params = $request->get_json_params();
    $name = sanitize_text_field($params['name']);
    $post = get_page_by_title($name, OBJECT, 'team_member');

    if ($post && $email = get_field('email', $post->ID)) {
        return ['email' => $email];
    } else {
        return new WP_Error('no_email', 'Email not found', array('status' => 404));
    }
}


add_action('wp_ajax_team_member_filter', 'theme_ajax_team_member_filter');
add_action('wp_ajax_nopriv_team_member_filter', 'theme_ajax_team_member_filter');

function theme_ajax_team_member_filter() {
    $args = array(
        'post_type' => 'team_member',
        'posts_per_page' => -1,
        'orderby' => 'title',
        'order' => 'ASC',
    );

    if (!empty($_GET['s'])) {
        $args['s'] = sanitize_text_field($_GET['s']);
    }

    $tax_query = [];

    if (!empty($_GET['position'])) {
        $tax_query[] = array(
            'taxonomy' => 'position',
            'field' => 'slug',
            'terms' => sanitize_text_field($_GET['position']),
        );
    }

    if (!empty($_GET['location'])) {
        $tax_query[] = array(
            'taxonomy' => 'location',
            'field' => 'slug',
            'terms' => sanitize_text_field($_GET['location']),
        );
    }

    if (!empty($_GET['service'])) {
        $tax_query[] = array(
            'taxonomy' => 'service',
            'field' => 'slug',
            'terms' => sanitize_text_field($_GET['service']),
        );
    }

    if (!empty($tax_query)) {
        $args['tax_query'] = $tax_query;
    }

    if (!empty($_GET['team_members']) && is_array($_GET['team_members'])) {
        $post__in = array_filter(array_map('intval', $_GET['team_members']));
        if (!empty($post__in)) {
            $args['post__in'] = $post__in;
            $args['orderby'] = 'post__in';
        }
    }

    $featured_ids = [];
    if (!empty($_GET['featured_team_members']) && is_array($_GET['featured_team_members'])) {
        $featured_ids = array_filter(array_map('intval', $_GET['featured_team_members']));
        if (!empty($featured_ids)) {
            $args['post__not_in'] = $featured_ids;
        }
    }

    ob_start();

    // Featured section
    if (!empty($featured_ids)) {
        $featured_args = $args;
        $featured_args['post__in'] = $featured_ids;
        $featured_args['orderby'] = 'post__in';

        $featured_query = new WP_Query($featured_args);
        if ($featured_query->have_posts()) {
            while ($featured_query->have_posts()) {
                $featured_query->the_post();
                get_template_part('part/team-member-card');
            }
        }
        wp_reset_postdata();
    }

    // Regular section
    $query = new WP_Query($args);
    $has_main_results = $query->have_posts();
    $has_featured_results = !empty($featured_query) && $featured_query->have_posts();

    if ($has_main_results) {
        while ($query->have_posts()) {
            $query->the_post();
            get_template_part('part/team-member-card');
        }
    }

    if (!$has_main_results && !$has_featured_results) {
        echo '<p>No team members found.</p>';
    }
    wp_reset_postdata();

    $html = ob_get_clean();
    wp_send_json_success($html);
}

// Enable one letter searches
add_filter( 'relevanssi_block_one_letter_searches', '__return_false' );
    <footer class="footer bottom-space-<?php the_field('bottom_space', 'option'); ?>">
        <div class="container">
            <div class="footer-1" data-grid-type="<?php the_field('grid_type', 'option'); ?>">
                <?php if($logo = get_field('footer_logo', 'option')): ?>
                <a href="<?php echo esc_url(get_home_url()); ?>" class="logo">
                    <img src="<?php echo $logo['url']; ?>" alt="<?php echo get_bloginfo('name'); ?>" width="189" height="37">
                </a>
                <?php endif; ?>
            </div>
            <div class="footer-2" data-grid-type="<?php the_field('grid_type', 'option'); ?>">
                <div class="footer-widgets">
                    <?php dynamic_sidebar('footer-widgets'); ?>
                </div>
                <div class="footer-widgets">
                    <?php dynamic_sidebar('footer-widgets-2'); ?>
                </div>
                <div class="footer-widgets">
                    <?php dynamic_sidebar('footer-widgets-3'); ?>
                </div>
                <div class="footer-widgets">
                    <?php dynamic_sidebar('footer-widgets-4'); ?>
                </div>
            </div>
            <div class="footer-3" data-grid-type="<?php the_field('grid_type', 'option'); ?>">
                <nav class="copyright-menu">
                    <?php theme_menu('copyright_menu', 1); ?>
                </nav>

                <div class="branding">
                    <?php the_field('branding', 'option'); ?>
                </div>

                <div class="copyright">
                    <?php echo str_replace('%year%', date('Y'), get_field('copyright', 'option')); ?>
                </div>
            </div>
        </div>
    </footer>

    <?php if(get_field('cookie_disclosure', 'option')): ?>
    <div class="cookie-disclosure">
        <div class="container">
            <div class="cookie-disclosure-inner">
                <div class="cookie-disclosure-message">
                    <?php the_field('cookie_disclosure_message', 'option'); ?>
                </div>
                <button class="button-light-filled close-cookie-disclosure">
                    <?php the_field('cookie_disclosure_button_label', 'option'); ?>
                </button>
            </div>
        </div>
    </div>
    <?php endif; ?>

</div>

<?php wp_footer(); ?>

</body>
</html>
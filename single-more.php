<?php get_header(); ?>

<?php while ( have_posts() ) : the_post(); ?>

<main id="page" class="default-template post-template">
    <div class="blocks-container">
        <ul class="breadcrumbs">
            <li>
                <a href="<?php echo esc_url(get_home_url()); ?>">
                    <?php _e('Home', 'mm-smy'); ?>
                </a>
            </li>
            <li>
                <?php $post_type = get_post_type(); ?>
                <a href="<?php echo get_post_type_archive_link($post_type);?>">
                    <?php
                        if($post_type == 'post'){
                            echo get_the_title(get_option('page_for_posts'));
                        }else{
                            $post_type_object = get_post_type_object($post_type);
                            echo $post_type_object->label;
                        }
                    ?>
                </a>
            </li>
            <?php
                $ancestors = get_post_ancestors(get_the_ID());
                if(!empty($ancestors)){
                    foreach($ancestors as $ancestor){
                        echo '<li><a href="'.get_the_permalink($ancestor).'">'.get_the_title($ancestor).'</a></li>';
                    }
                }
            ?>
            <li>
                <?php the_title(); ?>
            </li>
        </ul>
    </div>
    <article class="blocks-container">
        <?php the_title('<h1>', '</h1>'); ?>
        
        <?php if ( ! get_field( 'hide_date' ) ) : ?>
            <time class="post-date">
                <?php echo get_the_date(); ?>
            </time>
        <?php endif; ?>

        <?php if(has_post_thumbnail()): ?>
        <div class="main-post-image alignfull">
            <?php the_post_thumbnail('full'); ?>
        </div>
        <?php endif; ?>

        <?php the_content(); ?>
    </article>
    <div class="blocks-container">
        <div class="share-box<?php if(get_field('social_links')): echo ' hide'; endif; ?>">
            <h2><?php _e('Share:', 'mm-smy'); ?></h2>
            <ul>
                <li>
                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo "https://".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']; ?>" class="share-link">
                        <?php theme_asset('img/icon-facebook.svg'); ?>
                    </a>
                </li>
                <li>
                    <a href="http://twitter.com/share?url=<?php echo "https://".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']; ?>" class="share-link">
                        <?php theme_asset('img/icon-x.svg'); ?>
                    </a>
                </li>
                <li>
                    <a href="https://www.linkedin.com/shareArticle?mini=true&url=<?php echo "https://".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']; ?>" class="share-link">
                        <?php theme_asset('img/icon-linkedin.svg'); ?>
                    </a>
                </li>
            </ul>
        </div>

        <?php
            $post_tags = get_the_tags();
            if($post_tags):
        ?>
        <div class="tags-list">
            <h2><?php _e('Tags:', 'mm-smy'); ?></h2>
            <ul>
                <?php foreach($post_tags as $tag): ?>
                <li><a href="<?php echo get_tag_link($tag); ?>"><?php echo $tag->name; ?></a></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>

        <hr class="alignwide">

        <?php get_template_part('part/widgets', $post_type); ?>
    </div>
</main>

<?php endwhile; ?>

<?php get_footer();
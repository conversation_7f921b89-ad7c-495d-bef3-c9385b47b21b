<div id="<?php echo theme_block_id($block); ?>" class="<?php echo theme_block_class($block, 'debt-calculator alignfull'); ?>">
    <div class="container">
        <?php if(get_field('title') || get_field('eyebrow')): ?>
        <h2 data-animation>
            <?php if(get_field('eyebrow')): ?>
            <span class="subtitle">
                <?php the_field('eyebrow'); ?>
            </span>
            <?php endif; ?>

            <?php the_field('title'); ?>
        </h2>
        <?php endif; ?>

        <div class="debt-calculator-inner">
            <div class="debt-calculator-intro" data-animation>
                <?php the_field('intro'); ?>
            </div>
            <div class="debt-calculator-main" data-animation>
                <div class="debt-calculator-form">
                    <form>
                        <input type="hidden" name="osb-standard" value="<?php echo htmlspecialchars(json_encode(get_field('calculator_osb_standard')), ENT_QUOTES, 'UTF-8'); ?>">
                        <input type="hidden" name="consumer-proposal" value="<?php echo htmlspecialchars(json_encode(get_field('calculator_consumer_proposal_parameters')), ENT_QUOTES, 'UTF-8'); ?>">
                        <input type="hidden" name="consumer-proposal-description" value="<?php echo htmlspecialchars(get_field('calculator_consumer_proposal_labels_description'), ENT_QUOTES, 'UTF-8'); ?>">
                        <input type="hidden" name="bankruptcy" value="<?php echo htmlspecialchars(json_encode(get_field('calculator_bankruptcy_parameters')), ENT_QUOTES, 'UTF-8'); ?>">
                        <input type="hidden" name="bankruptcy-description" value="<?php echo htmlspecialchars(get_field('calculator_bankruptcy_labels_description'), ENT_QUOTES, 'UTF-8'); ?>">
                        <input type="hidden" name="regular-payment" value="<?php echo htmlspecialchars(json_encode(get_field('calculator_regular_payment_parameters')), ENT_QUOTES, 'UTF-8'); ?>">
                        <input type="hidden" name="regular-payment-description" value="<?php echo htmlspecialchars(get_field('calculator_regular_payment_labels_description'), ENT_QUOTES, 'UTF-8'); ?>">
                        <input type="hidden" name="consolidation-loan" value="<?php echo htmlspecialchars(json_encode(get_field('calculator_consolidation_loan_parameters')), ENT_QUOTES, 'UTF-8'); ?>">
                        <input type="hidden" name="consolidation-loan-description" value="<?php echo htmlspecialchars(get_field('calculator_consolidation_loan_labels_description'), ENT_QUOTES, 'UTF-8'); ?>">
                        <input type="hidden" name="credit-counseling-repayment" value="<?php echo htmlspecialchars(json_encode(get_field('calculator_credit_counseling_repayment_parameters')), ENT_QUOTES, 'UTF-8'); ?>">
                        <input type="hidden" name="credit-counseling-repayment-description" value="<?php echo htmlspecialchars(get_field('calculator_credit_counseling_repayment_labels_description'), ENT_QUOTES, 'UTF-8'); ?>">
                        <input type="hidden" name="years-label" value="<?php _e('years', 'mm-smy'); ?>">
                        <input type="hidden" name="months-label" value="<?php _e('months', 'mm-smy'); ?>">
                        <input type="hidden" name="none-label" value="<?php _e('none', 'mm-smy'); ?>">

                        <div class="form-group">
                            <label for="calculator-unsecured-debt">
                                <span>
                                    <?php the_field('calculator_total_unsecured_debt_label'); ?>
                                </span>

                                <?php if(get_field('calculator_total_unsecured_debt_help')): ?>
                                <button type="button" class="calculator-help" aria-label="<?php _e('Help', 'mm-smy'); ?>">?</button>
                                <?php endif; ?>
                            </label>

                            <?php if(get_field('calculator_total_unsecured_debt_help')): ?>
                            <p class="form-help">
                                <?php the_field('calculator_total_unsecured_debt_help'); ?>
                            </p>
                            <?php endif; ?>
                            
                            <input type="number" step="1" min="0" name="unsecured-debt" id="calculator-unsecured-debt" placeholder="<?php the_field('calculator_total_unsecured_debt_placeholder'); ?>" required>
                        </div>
                        <div class="form-group">
                            <label for="calculator-net-income">
                                <span>
                                    <?php the_field('calculator_monthly_net_income_after_tax_label'); ?>
                                </span>

                                <?php if(get_field('calculator_monthly_net_income_after_tax_help')): ?>
                                <button type="button" class="calculator-help" aria-label="<?php _e('Help', 'mm-smy'); ?>">?</button>
                                <?php endif; ?>
                            </label>

                            <?php if(get_field('calculator_monthly_net_income_after_tax_help')): ?>
                            <p class="form-help">
                                <?php the_field('calculator_monthly_net_income_after_tax_help'); ?>
                            </p>
                            <?php endif; ?>

                            <input type="number" step="1" min="0" name="net-income" id="calculator-net-income" placeholder="<?php the_field('calculator_monthly_net_income_after_tax_placeholder'); ?>" required>
                        </div>
                        <div class="form-group">
                            <label for="calculator-spousal-net-income">
                                <span>
                                    <?php the_field('calculator_spousal_monthly_net_income_after_tax_label'); ?>
                                </span>

                                <?php if(get_field('calculator_spousal_monthly_net_income_after_tax_help')): ?>
                                <button type="button" class="calculator-help" aria-label="<?php _e('Help', 'mm-smy'); ?>">?</button>
                                <?php endif; ?>
                            </label>

                            <?php if(get_field('calculator_spousal_monthly_net_income_after_tax_help')): ?>
                            <p class="form-help">
                                <?php the_field('calculator_spousal_monthly_net_income_after_tax_help'); ?>
                            </p>
                            <?php endif; ?>

                            <input type="number" step="1" min="0" name="spousal-net-income" id="calculator-spousal-net-income" placeholder="<?php the_field('calculator_spousal_monthly_net_income_after_tax_placeholder'); ?>" required>
                        </div>
                        <div class="form-group">
                            <label for="calculator-non-discretionary-expenses">
                                <span>
                                    <?php the_field('calculator_monthly_non-discretionary_expenses_label'); ?>
                                </span>

                                <?php if(get_field('calculator_monthly_non-discretionary_expenses_help')): ?>
                                <button type="button" class="calculator-help" aria-label="<?php _e('Help', 'mm-smy'); ?>">?</button>
                                <?php endif; ?>
                            </label>

                            <?php if(get_field('calculator_monthly_non-discretionary_expenses_help')): ?>
                            <p class="form-help">
                                <?php the_field('calculator_monthly_non-discretionary_expenses_help'); ?>
                            </p>
                            <?php endif; ?>
                            <input type="number" step="1" min="0" name="non-discretionary-expenses" id="calculator-non-discretionary-expenses" placeholder="<?php the_field('calculator_monthly_non-discretionary_expenses_placeholder'); ?>" required>
                        </div>
                        <div class="form-group">
                            <label for="calculator-people-in-household">
                                <span>
                                    <?php the_field('calculator_number_of_people_in_household_label'); ?>
                                </span>

                                <?php if(get_field('calculator_number_of_people_in_household_help')): ?>
                                <button type="button" class="calculator-help" aria-label="<?php _e('Help', 'mm-smy'); ?>">?</button>
                                <?php endif; ?>
                            </label>

                            <?php if(get_field('calculator_number_of_people_in_household_help')): ?>
                            <p class="form-help">
                                <?php the_field('calculator_number_of_people_in_household_help'); ?>
                            </p>
                            <?php endif; ?>
                            <input type="number" step="1" min="1" name="people-in-household" id="calculator-people-in-household" placeholder="<?php the_field('calculator_number_of_people_in_household_placeholder'); ?>" required>
                        </div>
                        <div class="form-group">
                            <label>
                                <span>
                                    <?php the_field('calculator_have_you_ever_been_bankrupt_before_label'); ?>
                                </span>

                                <?php if(get_field('calculator_have_you_ever_been_bankrupt_before_help')): ?>
                                <button type="button" class="calculator-help" aria-label="<?php _e('Help', 'mm-smy'); ?>">?</button>
                                <?php endif; ?>
                            </label>

                            <?php if(get_field('calculator_have_you_ever_been_bankrupt_before_help')): ?>
                            <p class="form-help">
                                <?php the_field('calculator_have_you_ever_been_bankrupt_before_help'); ?>
                            </p>
                            <?php endif; ?>

                            <div class="radio-group">
                                <label class="radio-label">
                                    <input type="radio" name="bankrupt-before" value="1" required>
                                    <div>
                                        <?php _e('Yes', 'mm-smy'); ?>
                                    </div>
                                </label>
                                <label class="radio-label">
                                    <input type="radio" name="bankrupt-before" value="0" required>
                                    <div>
                                        <?php _e('No', 'mm-smy'); ?>
                                    </div>
                                </label>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="button-light-filled">
                                <?php _e('Calculate', 'mm-smy'); ?>
                            </button>
                        </div>
                    </form>
                </div>
                <div class="debt-calculator-results">
                    <?php if($logo = get_field('header_logo', 'option')): ?>
                    <div class="debt-calculator-logo">
                        <img src="<?php echo $logo['url']; ?>" alt="<?php echo get_bloginfo('name'); ?>">
                    </div>
                    <?php endif; ?>

                    <div class="debt-calculator-results-graphic">
                        <div class="debt-calculator-results-bar debt-calculator-results-bar-regular-payment">
                            <div class="debt-calculator-results-bar-total"></div>    
                            <div class="debt-calculator-results-bar-description">
                                <?php if(get_field('calculator_regular_payment_labels_title')): ?>
                                <h3><?php the_field('calculator_regular_payment_labels_title'); ?></h3>
                                <?php endif; ?>
                                <p></p>
                            </div>    
                        </div>
                        <div class="debt-calculator-results-bar debt-calculator-results-bar-consolidation-loan">
                            <div class="debt-calculator-results-bar-total"></div>    
                            <div class="debt-calculator-results-bar-description">
                                <?php if(get_field('calculator_consolidation_loan_labels_title')): ?>
                                <h3><?php the_field('calculator_consolidation_loan_labels_title'); ?></h3>
                                <?php endif; ?>
                                <p></p>
                            </div>    
                        </div>
                        <div class="debt-calculator-results-bar debt-calculator-results-bar-credit-counseling-repayment">
                            <div class="debt-calculator-results-bar-total"></div>    
                            <div class="debt-calculator-results-bar-description">
                                <?php if(get_field('calculator_credit_counseling_repayment_labels_title')): ?>
                                <h3><?php the_field('calculator_credit_counseling_repayment_labels_title'); ?></h3>
                                <?php endif; ?>
                                <p></p>
                            </div>    
                        </div>
                        <div class="debt-calculator-results-bar debt-calculator-results-bar-consumer-proposal">
                            <div class="debt-calculator-results-bar-total"></div>    
                            <div class="debt-calculator-results-bar-description">
                                <?php if(get_field('calculator_consumer_proposal_labels_title')): ?>
                                <h3><?php the_field('calculator_consumer_proposal_labels_title'); ?></h3>
                                <?php endif; ?>
                                <p></p>
                            </div>    
                        </div>
                        <div class="debt-calculator-results-bar debt-calculator-results-bar-bankruptcy">
                            <div class="debt-calculator-results-bar-total"></div>    
                            <div class="debt-calculator-results-bar-description">
                                <?php if(get_field('calculator_bankruptcy_labels_title')): ?>
                                <h3><?php the_field('calculator_bankruptcy_labels_title'); ?></h3>
                                <?php endif; ?>
                                <p></p>
                            </div>    
                        </div>
                        <div class="debt-calculator-results-label">
                            <?php the_field('calculator_regular_payment_labels_title'); ?>
                        </div>
                        <div class="debt-calculator-results-label">
                            <?php the_field('calculator_consolidation_loan_labels_title'); ?>
                        </div>
                        <div class="debt-calculator-results-label">
                            <?php the_field('calculator_credit_counseling_repayment_labels_title'); ?>
                        </div>
                        <div class="debt-calculator-results-label">
                            <?php the_field('calculator_consumer_proposal_labels_title'); ?>
                        </div>
                        <div class="debt-calculator-results-label">
                            <?php the_field('calculator_bankruptcy_labels_title'); ?>
                        </div>
                    </div>

                    <div class="debt-calculator-results-table-container">
                        <div class="debt-calculator-results-table">
                            <?php if(get_field('calculator_consumer_proposal_labels_title')): ?>
                            <h3><?php the_field('calculator_consumer_proposal_labels_title'); ?></h3>
                            <?php endif; ?>

                            <div class="debt-calculator-results-table-grid">
                                <p class="consumer-proposal-description"></p>

                                <dl>
                                    <?php if(get_field('calculator_consumer_proposal_labels_monthly_payment')): ?>
                                    <dt><?php the_field('calculator_consumer_proposal_labels_monthly_payment'); ?></dt>
                                    <dd class="consumer-proposal-monthly-payment"></dd>
                                    <?php endif; ?>
                                    <?php if(get_field('calculator_consumer_proposal_labels_total_payment')): ?>
                                    <dt><?php the_field('calculator_consumer_proposal_labels_total_payment'); ?></dt>
                                    <dd class="consumer-proposal-total-payment"></dd>
                                    <?php endif; ?>
                                    <?php if(get_field('calculator_consumer_proposal_labels_interest_rate')): ?>
                                    <dt><?php the_field('calculator_consumer_proposal_labels_interest_rate'); ?></dt>
                                    <dd class="consumer-proposal-interest-rate"></dd>
                                    <?php endif; ?>
                                    <?php if(get_field('calculator_consumer_proposal_labels_payback_period')): ?>
                                    <dt><?php the_field('calculator_consumer_proposal_labels_payback_period'); ?></dt>
                                    <dd class="consumer-proposal-payback-period"></dd>
                                    <?php endif; ?>
                                </dl>
                            </div>
                        </div>
                        <div class="debt-calculator-results-table">
                            <?php if(get_field('calculator_bankruptcy_labels_title')): ?>
                            <h3><?php the_field('calculator_bankruptcy_labels_title'); ?></h3>
                            <?php endif; ?>

                            <div class="debt-calculator-results-table-grid">
                                <p class="bankruptcy-description"></p>

                                <dl>
                                    <?php if(get_field('calculator_bankruptcy_labels_monthly_payment')): ?>
                                    <dt><?php the_field('calculator_bankruptcy_labels_monthly_payment'); ?></dt>
                                    <dd class="bankruptcy-monthly-payment"></dd>
                                    <?php endif; ?>
                                    <?php if(get_field('calculator_bankruptcy_labels_total_payment')): ?>
                                    <dt><?php the_field('calculator_bankruptcy_labels_total_payment'); ?></dt>
                                    <dd class="bankruptcy-total-payment"></dd>
                                    <?php endif; ?>
                                    <?php if(get_field('calculator_bankruptcy_labels_interest_rate')): ?>
                                    <dt><?php the_field('calculator_bankruptcy_labels_interest_rate'); ?></dt>
                                    <dd class="bankruptcy-interest-rate"></dd>
                                    <?php endif; ?>
                                    <?php if(get_field('calculator_bankruptcy_labels_payback_period')): ?>
                                    <dt><?php the_field('calculator_bankruptcy_labels_payback_period'); ?></dt>
                                    <dd class="bankruptcy-payback-period"></dd>
                                    <?php endif; ?>
                                </dl>
                            </div>
                        </div>
                        <div class="debt-calculator-results-table">
                            <?php if(get_field('calculator_regular_payment_labels_title')): ?>
                            <h3><?php the_field('calculator_regular_payment_labels_title'); ?></h3>
                            <?php endif; ?>

                            <div class="debt-calculator-results-table-grid">
                                <p class="regular-payment-description"></p>

                                <dl>
                                    <?php if(get_field('calculator_regular_payment_labels_monthly_payment')): ?>
                                    <dt><?php the_field('calculator_regular_payment_labels_monthly_payment'); ?></dt>
                                    <dd class="regular-payment-monthly-payment"></dd>
                                    <?php endif; ?>
                                    <?php if(get_field('calculator_regular_payment_labels_total_payment')): ?>
                                    <dt><?php the_field('calculator_regular_payment_labels_total_payment'); ?></dt>
                                    <dd class="regular-payment-total-payment"></dd>
                                    <?php endif; ?>
                                    <?php if(get_field('calculator_regular_payment_labels_interest_rate')): ?>
                                    <dt><?php the_field('calculator_regular_payment_labels_interest_rate'); ?></dt>
                                    <dd class="regular-payment-interest-rate"></dd>
                                    <?php endif; ?>
                                    <?php if(get_field('calculator_regular_payment_labels_payback_period')): ?>
                                    <dt><?php the_field('calculator_regular_payment_labels_payback_period'); ?></dt>
                                    <dd class="regular-payment-payback-period"></dd>
                                    <?php endif; ?>
                                </dl>
                            </div>
                        </div>
                        <div class="debt-calculator-results-table">
                            <?php if(get_field('calculator_consolidation_loan_labels_title')): ?>
                            <h3><?php the_field('calculator_consolidation_loan_labels_title'); ?></h3>
                            <?php endif; ?>

                            <div class="debt-calculator-results-table-grid">
                                <p class="consolidation-loan-description"></p>

                                <dl>
                                    <?php if(get_field('calculator_consolidation_loan_labels_monthly_payment')): ?>
                                    <dt><?php the_field('calculator_consolidation_loan_labels_monthly_payment'); ?></dt>
                                    <dd class="consolidation-loan-monthly-payment"></dd>
                                    <?php endif; ?>
                                    <?php if(get_field('calculator_consolidation_loan_labels_total_payment')): ?>
                                    <dt><?php the_field('calculator_consolidation_loan_labels_total_payment'); ?></dt>
                                    <dd class="consolidation-loan-total-payment"></dd>
                                    <?php endif; ?>
                                    <?php if(get_field('calculator_consolidation_loan_labels_interest_rate')): ?>
                                    <dt><?php the_field('calculator_consolidation_loan_labels_interest_rate'); ?></dt>
                                    <dd class="consolidation-loan-interest-rate"></dd>
                                    <?php endif; ?>
                                    <?php if(get_field('calculator_consolidation_loan_labels_payback_period')): ?>
                                    <dt><?php the_field('calculator_consolidation_loan_labels_payback_period'); ?></dt>
                                    <dd class="consolidation-loan-payback-period"></dd>
                                    <?php endif; ?>
                                </dl>
                            </div>
                        </div>
                        <div class="debt-calculator-results-table">
                            <?php if(get_field('calculator_credit_counseling_repayment_labels_title')): ?>
                            <h3><?php the_field('calculator_credit_counseling_repayment_labels_title'); ?></h3>
                            <?php endif; ?>

                            <div class="debt-calculator-results-table-grid">
                                <p class="credit-counseling-repayment-description"></p>

                                <dl>
                                    <?php if(get_field('calculator_credit_counseling_repayment_labels_monthly_payment')): ?>
                                    <dt><?php the_field('calculator_credit_counseling_repayment_labels_monthly_payment'); ?></dt>
                                    <dd class="credit-counseling-repayment-monthly-payment"></dd>
                                    <?php endif; ?>
                                    <?php if(get_field('calculator_credit_counseling_repayment_labels_total_payment')): ?>
                                    <dt><?php the_field('calculator_credit_counseling_repayment_labels_total_payment'); ?></dt>
                                    <dd class="credit-counseling-repayment-total-payment"></dd>
                                    <?php endif; ?>
                                    <?php if(get_field('calculator_credit_counseling_repayment_labels_interest_rate')): ?>
                                    <dt><?php the_field('calculator_credit_counseling_repayment_labels_interest_rate'); ?></dt>
                                    <dd class="credit-counseling-repayment-interest-rate"></dd>
                                    <?php endif; ?>
                                    <?php if(get_field('calculator_credit_counseling_repayment_labels_payback_period')): ?>
                                    <dt><?php the_field('calculator_credit_counseling_repayment_labels_payback_period'); ?></dt>
                                    <dd class="credit-counseling-repayment-payback-period"></dd>
                                    <?php endif; ?>
                                </dl>
                            </div>
                        </div>
                    </div>
                
                    <div class="debt-calculator-actions">
                        <button class="button-light-filled debt-calculator-try-again">
                            <?php _e('Try another calculation', 'mm-smy'); ?>
                        </button>
                        <button class="button-light-outline debt-calculator-print">
                            <?php _e('Save or Print', 'mm-smy'); ?>
                        </button>
                    </div>
                </div>
                <div class="debt-calculator-disclaimer">
                    <?php the_field('disclaimer'); ?>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="<?php echo theme_block_id($block); ?>" class="<?php echo theme_block_class($block, 'posts-section alignfull'); ?>">
    <div class="container">
        <header data-animation>
            <?php if(get_field('title')): ?>
            <h2><?php the_field('title'); ?></h2>
            <?php endif; ?>

            <?php if(get_field('enable_filters')): ?>
            <button class="button-light-outline posts-filter-toggle">
                <?php the_field('filter_button_label'); ?>
            </button>
            <?php endif; ?>
        </header>

        <?php if(get_field('enable_filters')): ?>
        <div class="posts-filters">
            <div class="posts-filters-inner">
                <?php
                    $taxonomies = get_field('filters');

                    foreach($taxonomies as $taxonomy):
                ?>
                <?php
                    $taxonomy_object = get_taxonomy($taxonomy);
                    $label = $taxonomy_object->labels->singular_name;

                    $tax = get_terms(array(
                        'taxonomy' => $taxonomy,
                        'hide_empty' => true,
                    ));
                ?>
                <?php if(!empty($tax)): ?>
                <div class="posts-filters-group">
                    <h3><?php _e('Filter by '.$label, 'mm-smy'); ?></h3>
                    <ul>
                        <?php foreach($tax as $term): ?>
                        <li>
                            <a href="<?php echo toggle_query_arg($taxonomy_object->query_var, $term->slug); ?>" <?php if(is_tax($taxonomy, $term) || in_array($term->slug, explode(',', get_query_var($taxonomy)))) echo 'class="active"'; ?> rel="nofollow">
                                <?php echo $term->name; ?>
                            </a>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <div class="posts-section-container" data-animation>
            <?php
                if(!is_singular()){
                    global $wp_query;
                    $query = $wp_query;
                }else{
                    $args = array(
                        'post_type' => get_field('post_type'),
                        'paged' => (get_query_var('paged')) ? get_query_var('paged') : 1,
                        'tax_query' => array(),
                    );

                    if(get_query_var('service')){
                        $args['tax_query'][] = array(
                            'taxonomy' => 'service',
                            'field' => 'slug',
                            'terms' => explode(',', get_query_var('service')),
                        );
                    }

                    if(get_query_var('industry')){
                        $args['tax_query'][] = array(
                            'taxonomy' => 'industry',
                            'field' => 'slug',
                            'terms' => explode(',', get_query_var('industry')),
                        );
                    }

                    if(get_query_var('news')){
                        $args['tax_query'][] = array(
                            'taxonomy' => 'news',
                            'field' => 'slug',
                            'terms' => explode(',', get_query_var('news')),
                        );
                    }

                    $query = new WP_Query($args);
                }
            ?>
            <?php if($query->have_posts()): ?>
                <div class="posts-section-grid">
                    <?php while ( $query->have_posts() ) : $query->the_post(); ?>
                    <?php get_template_part('part/post'); ?>
                    <?php endwhile; ?>
                </div>
                <?php if(!is_singular()): ?>
                    <?php the_posts_pagination(); ?>
                <?php else: ?>
                    <div class="pagination">
                        <div class="nav-links">
                            <?php
                                echo paginate_links( array(
                                    'base' => str_replace( PHP_INT_MAX, '%#%', esc_url( get_pagenum_link( PHP_INT_MAX ) ) ),
                                    'format' => '?paged=%#%',
                                    'current' => (get_query_var('paged')) ? get_query_var('paged') : 1,
                                    'total' => $query->max_num_pages,
                                    'prev_text' => __('Previous', 'mm-smy'),
                                    'next_text' => __('Next', 'mm-smy')
                                ) );
                            ?>
                        </div>
                    </div>
                <?php endif; ?>
            <?php else: ?>
            <p><?php _e('No content found.', 'mm-smy'); ?></p>
            <?php endif; ?>
            <?php if(is_singular()) wp_reset_postdata(); ?>
        </div>
    </div>
</div>
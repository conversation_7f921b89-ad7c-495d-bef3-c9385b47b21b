<div id="<?php echo theme_block_id($block); ?>" class="<?php echo theme_block_class($block, 'text-table-3-columns alignfull'); ?>">
    <div class="container">
        <div class="text-table-3-columns-inner">
            <div class="text-table-3-columns-intro" data-animation>
                <?php if(get_field('title') || get_field('eyebrow')): ?>
                <h2>
                    <?php if(get_field('eyebrow')): ?>
                    <span class="subtitle">
                        <?php the_field('eyebrow'); ?>
                    </span>
                    <?php endif; ?>

                    <?php the_field('title'); ?>
                </h2>
                <?php endif; ?>

                <?php if(get_field('description')): ?>
                <p><?php the_field('description'); ?></p>
                <?php endif; ?>

                <?php if($button = get_field('button')): ?>
                <a class="button-light-filled" href="<?php echo $button['url']; ?>" target="<?php echo $button['target'] ? $button['target'] : '_self'; ?>">
                    <?php echo $button['title']; ?>
                </a>
                <?php endif; ?>

            </div>
            <?php if(have_rows('columns')): ?>
            <div class="text-table-3-columns-table">
                <?php while(have_rows('columns')): the_row(); ?>
                <div class="text-table-3-columns-column">
                    <?php $items = get_sub_field('items'); ?>
                    <?php if(!empty($items)): ?>
                        <?php foreach($items as $item): ?>
                            <?php if($item['acf_fc_layout'] == 'content_with_link'): ?>
                                <?php echo $item['content']; ?>
                            <?php elseif($item['acf_fc_layout'] == 'content'): ?>
                                <p data-animation><?php echo $item['content']; ?></p>
                                <?php if($item['button']): ?>
                                <a class="button-light-outline" href="<?php echo $item['button']['url']; ?>" target="<?php echo $item['button']['target'] ? $item['button']['target'] : '_self'; ?>">
                                    <?php echo $item['button']['title']; ?>
                                </a>
                                <?php endif; ?>

                            <?php endif; ?>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                <?php endwhile; ?>
            </div>
            <?php endif; ?>
        </div>

    </div>
</div>
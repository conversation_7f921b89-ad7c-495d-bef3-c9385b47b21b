<div id="<?php echo theme_block_id($block); ?>" class="<?php echo theme_block_class($block, 'tabs-section alignfull'); ?>">
    <div class="container">
        <div class="tabs-section-inner" data-background="<?php the_field('background'); ?>" data-animation>
            <header>
                <?php if(get_field('title') || get_field('eyebrow')): ?>
                <h2>
                    <?php if(get_field('eyebrow')): ?>
                    <span class="subtitle">
                        <?php the_field('eyebrow'); ?>
                    </span>
                    <?php endif; ?>

                    <?php the_field('title'); ?>
                </h2>
                <?php endif; ?>

                <?php if(get_field('subheading')): ?>
                <h3><?php the_field('subheading'); ?></h3>
                <?php endif; ?>

                <?php the_field('description'); ?>
            </header>

            <?php if(have_rows('tabs')): ?>
            <div class="tabs-section-container">
                <div class="tabs-section-container-tabs" role="tablist" aria-label="<?php the_field('title'); ?>">
                    <?php $i = 0; while(have_rows('tabs')): the_row(); ?>
                    <button class="tabs-section-tab-selector <?php if($i == 0) echo ' active'; ?>" role="tab" id="<?php echo theme_block_id($block).'-tab-'.$i; ?>" aria-controls="<?php echo theme_block_id($block).'-tabpanel-'.$i; ?>" aria-selected="<?php if($i == 0) {echo 'true';}else{echo 'false';} ?>" tabindex="<?php if($i == 0) {echo '0';}else{echo '-1';} ?>">
                        <?php the_sub_field('tab_title'); ?>
                    </button>
                    <?php $i++; endwhile; ?>
                </div>
                <div class="tabs-section-container-content">
                    <?php $i = 0; while(have_rows('tabs')): the_row(); ?>
                    <div class="tabs-section-container-item <?php if($i == 0) echo ' active'; ?>" role="tabpanel" id="<?php echo theme_block_id($block).'-tabpanel-'.$i; ?>" aria-labelledby="<?php echo theme_block_id($block).'-tab-'.$i; ?>" tabindex="<?php if($i == 0) {echo '0';}else{echo '-1';} ?>">
                        <?php if(get_sub_field('title')): ?>
                        <h3><?php the_sub_field('title'); ?></h3>
                        <?php endif; ?>

                        <?php if(get_sub_field('content')): ?>
                            <?php the_sub_field('content'); ?>
                        <?php endif; ?>

                        <?php if($image = get_sub_field('image')): ?>
                        <img src="<?php echo $image['url']; ?>" alt="<?php echo $image['alt']; ?>" loading="lazy">
                        <?php endif; ?>

                        <?php if($button = get_sub_field('button')): ?>
                        <a class="button-light-outline" href="<?php echo $button['url']; ?>" target="<?php echo $button['target'] ? $button['target'] : '_self'; ?>">
                            <?php echo $button['title']; ?>
                        </a>
                        <?php endif; ?>
                    </div>
                    <?php $i++; endwhile; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>

    </div>
</div>
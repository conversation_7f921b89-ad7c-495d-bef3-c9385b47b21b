<div id="<?php echo theme_block_id($block); ?>" class="<?php echo theme_block_class($block, 'testimonials-carousel alignfull'); ?>">
    <div class="container">
        <?php if(have_rows('testimonials')): ?>
        <div class="testimonials-carousel-inner" data-image-position="<?php the_field('image_position'); ?>" data-animation>
            <div class="testimonials-carousel-image-container">
                <div class="swiper testimonials-carousel-image-swiper">
                    <div class="swiper-wrapper">
                        <?php while(have_rows('testimonials')): the_row(); ?>
                        <div class="swiper-slide">
                            <?php if($image = get_sub_field('image')): ?>
                            <img src="<?php echo $image['url']; ?>" alt="<?php echo $image['alt']; ?>" loading="lazy">
                            <?php endif; ?>
                        </div>
                        <?php endwhile; ?>
                    </div>
                </div>

                <?php if(get_field('title')): ?>
                <div class="testimonials-carousel-title">
                    <h2><?php the_field('title'); ?></h2>
                </div>
                <?php endif; ?>
            </div>
            <div class="testimonials-carousel-quote-container">
                <div class="swiper testimonials-carousel-swiper">
                    <div class="swiper-wrapper">
                        <?php while(have_rows('testimonials')): the_row(); ?>
                        <div class="swiper-slide" data-font-size="<?php the_sub_field('font_size'); ?>">
                            <?php if(get_sub_field('quote')): ?>
                            <p><?php the_sub_field('quote'); ?></p>
                            <?php endif; ?>

                            <?php if(get_sub_field('name') || get_sub_field('position')): ?>
                            <h3>
                                <?php
                                    if(get_sub_field('name')){
                                        echo '<strong>'.get_sub_field('name').'</strong>';
                                    }

                                    if(get_sub_field('name') && get_sub_field('position')){
                                        echo ', ';
                                    }

                                    the_sub_field('position');
                                ?>
                            </h3>
                            <?php endif; ?>
                        </div>
                        <?php endwhile; ?>
                    </div>
                </div>

                <div class="testimonials-carousel-quote-navigation">
                    <button class="testimonials-carousel-quote-prev">
                        <?php theme_asset('img/carousel-arrow-prev.svg'); ?>
                    </button>
                    <button class="testimonials-carousel-quote-next">
                        <?php theme_asset('img/carousel-arrow-next.svg'); ?>
                    </button>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>
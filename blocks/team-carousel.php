<div id="<?php echo theme_block_id($block); ?>" class="<?php echo theme_block_class($block, 'team-carousel alignfull'); ?>">
    <div class="swiper team-swiper">
        <div class="container">
            <header>
                <?php if(get_field('title')): ?>
                <h2><?php the_field('title'); ?></h2>
                <?php endif; ?>

                <div class="team-swiper-navigation">
                    <button class="team-swiper-prev">
                        <?php theme_asset('img/carousel-arrow-prev-circle.svg'); ?>
                    </button>
                    <button class="team-swiper-next">
                        <?php theme_asset('img/carousel-arrow-next-circle.svg'); ?>
                    </button>
                </div>
            </header>
        </div>

        <?php
            $args = array(
                'post_type' => 'team_member',
                'posts_per_page' => -1,
                'post__in' => get_field('team_members'),
                'orderby' => 'post__in',
            );

            $query = new WP_Query($args);
        ?>
        <?php if( $query->have_posts() ): ?>
        <div class="swiper-wrapper">
            <?php while ($query->have_posts()) : $query->the_post(); ?>
            <div class="swiper-slide team-member-card">
                <div class="team-member-card-image">
                    <?php the_post_thumbnail('full'); ?>
                </div>
                <div class="team-member-card-information">
                    <h3><?php the_title(); ?></h3>

                    <?php if($positions = get_the_terms(get_the_ID(), 'position')): ?>
                    <p><?php echo implode(', ', wp_list_pluck($positions, 'name')); ?></p>
                    <?php endif; ?>

                    <?php if($locations = get_the_terms(get_the_ID(), 'location')): ?>
                    <p><?php echo implode(', ', wp_list_pluck($locations, 'name')); ?></p>
                    <?php endif; ?>

                    <?php if($services = get_the_terms(get_the_ID(), 'service')): ?>
                    <p><?php echo implode(', ', wp_list_pluck($services, 'name')); ?></p>
                    <?php endif; ?>

                    <?php if(get_field('email', get_the_ID())): ?>
                    <a href="#" class="email-link" data-name="<?php the_title(); ?>">
                        <?php theme_asset('img/icon-email.svg'); ?>
                    </a>
                    <?php endif; ?>

                    <?php if(get_field('linkedin', get_the_ID())): ?>
                    &nbsp;
                    <a href="<?php the_field('linkedin', get_the_ID()); ?>" target="_blank">
                        <?php theme_asset('img/icon-linkedin.svg'); ?>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endwhile; ?>
        </div>
        <?php endif; ?>
        <?php wp_reset_postdata(); ?>
    </div>
</div>
<div id="<?php echo theme_block_id($block); ?>" class="<?php echo theme_block_class($block, 'primary-hero alignfull'); ?>">
    <div class="container">
        <div class="primary-hero-container">
            <?php if( have_rows('backgrounds') ): ?>
            <div class="swiper primary-hero-slider">
                <div class="swiper-wrapper">
                    <?php while(have_rows('backgrounds')): the_row(); ?>
                        <?php if(get_row_layout() == 'image'): ?>
                        <?php 
                            $image = get_sub_field('image'); 
                        ?>
                        <div class="swiper-slide" data-swiper-autoplay="5000">
                            <img src="<?php echo $image['url']; ?>" alt="<?php echo $image['alt']; ?>">
                        </div>
                        <?php elseif(get_row_layout() == 'video'): ?>
                        <?php 
                            $video = get_sub_field('video'); 
                            $video_meta = wp_get_attachment_metadata($video['ID']);
                        ?>
                        <div class="swiper-slide" data-swiper-autoplay="<?php echo ($video_meta['length'] * 1000) - 500; ?>" data-video>
                            <video muted playsinline>
                                <source src="<?php echo $video['url']; ?>" type="<?php echo $video['mime_type']; ?>">
                            </video>
                        </div>  
                        <?php endif; ?>
                    <?php endwhile; ?>
                </div>
                <div class="swiper-pagination"></div>
            </div>
            <?php endif; ?>
            
            <?php if(get_field('headline') || get_field('intro')): ?>
            <div class="primary-hero-inner">
                <?php if(get_field('headline')): ?>
                <h1><?php the_field('headline'); ?></h1>
                <?php endif; ?>

                <?php if(get_field('intro')): ?>
                <p><?php the_field('intro'); ?></p>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
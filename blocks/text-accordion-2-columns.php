<div id="<?php echo theme_block_id($block); ?>" class="<?php echo theme_block_class($block, 'text-accordion-2-columns alignfull'); ?>">
    <div class="container">
        <div class="text-accordion-2-columns-inner">
            <div class="text-accordion-2-columns-intro">
                <?php if(get_field('title')): ?>
                <h2><?php the_field('title'); ?></h2>
                <?php endif; ?>

                <?php if(get_field('description')): ?>
                <p><?php the_field('description'); ?></p>
                <?php endif; ?>

                <?php if($button = get_field('button')): ?>
                <a class="button-light-filled" href="<?php echo $button['url']; ?>" target="<?php echo $button['target'] ? $button['target'] : '_self'; ?>">
                    <?php echo $button['title']; ?>
                </a>
                <?php endif; ?>
            </div>
            <?php if(have_rows('accordions')): ?>
                <div class="text-accordion-2-columns-content">
                    <?php while(have_rows('accordions')): the_row(); ?>
                        <div class="accordion">
                            <?php if(get_sub_field('content')) : ?>
                                <button>
                                    <?php the_sub_field('title'); ?>
                                </button>
                                <div>
                                    <?php the_sub_field('content'); ?>
                                    
                                    <?php if($contentButton = get_sub_field('button')): ?>
                                    <a class="button-light-outline" href="<?php echo $contentButton['url']; ?>" target="<?php echo $contentButton['target'] ? $contentButton['target'] : '_self'; ?>">
                                        <?php echo $contentButton['title']; ?> <!-- Corrected here -->
                                    </a>
                                    <?php endif; ?>

                                </div>
                                <?php else : ?>
                                    <?php if($titleLink = get_sub_field('link')): ?>
                                        <a href="<?php echo $titleLink['url']; ?>" target="<?php echo $titleLink['target'] ? $titleLink['target'] : '_self'; ?>">
                                            <?php the_sub_field('title'); ?>
                                        </a>
                                    <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    <?php endwhile; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
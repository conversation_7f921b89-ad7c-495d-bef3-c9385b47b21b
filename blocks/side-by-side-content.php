<div id="<?php echo theme_block_id($block); ?>" class="<?php echo theme_block_class($block, 'side-by-side-content alignfull'); ?>">
    <div class="container">
        <?php if (have_rows('left_content') || have_rows('right_content')): ?>
            <div class="side-by-side-content-inner">
                <?php if (have_rows('left_content')): ?>
                    <div class="side-by-side-content-left" data-animation>
                        <?php while (have_rows('left_content')): the_row(); ?>
                            <?php if(get_field('left_content_title')): ?>
                                <h2><?php the_field('left_content_title'); ?></h2>
                            <?php endif; ?>

                            <?php if(get_field('left_content_content')): ?>
                                <?php the_field('left_content_content'); ?>
                            <?php endif; ?>
                        <?php endwhile; ?>
                    </div>
                <?php endif; ?>
                <?php if (have_rows('right_content')): ?>
                    <div class="side-by-side-content-right" data-animation>
                        <?php while (have_rows('right_content')): the_row(); ?>
                            <div class="side-by-side-content-right-item">
                                <?php if(get_sub_field('number') || get_sub_field('title')): ?>
                                    <div class="side-by-side-content-right-number">
                                        <?php if(get_sub_field('number')): ?>
                                            <h2><?php the_sub_field('number'); ?></h2>
                                        <?php endif; ?>
                                    </div>
                                    <div class="side-by-side-content-right-title">
                                        <?php if(get_sub_field('title')): ?>
                                            <h3><?php the_sub_field('title'); ?></h3>
                                        <?php endif; ?>
                                        <?php if(get_sub_field('content')): ?>
                                            <?php the_sub_field('content'); ?>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endwhile; ?>
                    </div>
                <?php endif; ?>
            </div> 
        <?php endif; ?>

        <?php $image = get_field('background_image'); ?>
        <?php if (!empty($image)) : ?>
            <div class="side-by-side-content-background">
                <img src="<?php echo $image['url']; ?>" alt="<?php echo $image['alt']; ?>">
            </div>
        <?php endif; ?>
    </div>
</div>
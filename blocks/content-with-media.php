<div id="<?php echo theme_block_id($block); ?>" class="<?php echo theme_block_class($block, 'content-with-media alignfull'); ?>">
    <div class="container">
        <div class="content-with-media-inner" data-media-position="<?php the_field('media_position'); ?>">
            <div class="content-with-media-media" data-animation>
                <?php if(get_field('image') || get_field('video') || get_field('location') || get_field('embedded_url')): ?>
                <div class="content-with-media-media-container">
                    <?php if(get_field('media_type') == 'image' && $image = get_field('image')): ?>
                    <img src="<?php echo $image['url']; ?>" alt="<?php echo $image['alt']; ?>" width="100%" height="500" loading="lazy">
                    <?php elseif(get_field('media_type') == 'video' && $video = get_field('video')): ?>
                    <video muted playsinline loop autoplay>
                        <source src="<?php echo $video['url']; ?>" type="<?php echo $video['mime_type']; ?>">
                    </video>
                    <?php elseif(get_field('media_type') == 'map' && $location = get_field('location')): ?>
                    <div class="map-outer">
                        <div class="map" data-zoom="<?php echo esc_attr($location['zoom']); ?>" data-key="<?php the_field('google_maps_api_key', 'option'); ?>">
                            <div class="marker" data-lat="<?php echo esc_attr($location['lat']); ?>" data-lng="<?php echo esc_attr($location['lng']); ?>" data-marker="<?php echo get_template_directory_uri().'/img/map-marker.svg'; ?>"></div>
                        </div>
                    </div>
                    <?php elseif(get_field('media_type') == 'embedded' && $embedded_url = get_field('embedded_url')): ?>
                        <iframe src="<?php echo esc_attr($embedded_url); ?>" frameborder="0" allow="autoplay; encrypted-media" allowfullscreen></iframe>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
            <div class="content-with-media-content" data-animation>
                <?php if(get_field('eyebrow')): ?>
                    <span class="subtitle">
                        <?php the_field('eyebrow'); ?>
                    </span>
                <?php endif; ?>
                <?php if(get_field('title')): ?>
                <h2><?php the_field('title'); ?></h2>
                <?php endif; ?>

                <?php the_field('content'); ?>

                <?php if(have_rows('list')): ?>
                <div class="content-with-media-list">
                    <?php while(have_rows('list')): the_row(); ?>
                    <div class="content-with-media-list-item">
                        <?php if($button = get_sub_field('link')): ?>
                        <a href="<?php echo $button['url']; ?>" target="<?php echo $button['target'] ? $button['target'] : '_self'; ?>">
                        <?php endif; ?>
                            <?php if(get_sub_field('title') || !empty($button['title'])): ?>
                            <h3>
                                <?php
                                    if(get_sub_field('title')){
                                        the_sub_field('title');
                                    }else{
                                        echo $button['title'];
                                    }
                                ?>
                            </h3>
                            <?php endif; ?>
                        <?php if($button = get_sub_field('link')): ?>
                        </a>
                        <?php endif; ?>

                        <?php the_sub_field('content'); ?>
                    </div>
                    <?php endwhile; ?>
                </div>
                <?php endif; ?>

                <?php if($button = get_field('button')): ?>
                <a class="button-light-outline" href="<?php echo $button['url']; ?>" target="<?php echo $button['target'] ? $button['target'] : '_self'; ?>">
                    <?php echo $button['title']; ?>
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
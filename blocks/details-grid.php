<div id="<?php echo theme_block_id($block); ?>" class="<?php echo theme_block_class($block, 'details-grid alignfull'); ?>">
    <div class="container">
        <div class="details-grid-inner" data-background="<?php the_field('background'); ?>" data-animation>
            <header>
                <?php if(get_field('title') || get_field('eyebrow')): ?>
                <h2>
                    <?php if(get_field('eyebrow')): ?>
                    <span class="subtitle">
                        <?php the_field('eyebrow'); ?>
                    </span>
                    <?php endif; ?>

                    <?php the_field('title'); ?>
                </h2>
                <?php endif; ?>

                <?php if(get_field('intro')): ?>
                <p><?php the_field('intro'); ?></p>
                <?php endif; ?>
            </header>

            <?php if(have_rows('details')): ?>
            <div class="details-grid-grid" data-items="<?php the_field('details_per_row'); ?>" data-alignment="<?php the_field('details_alignment'); ?>">
                <?php while(have_rows('details')): the_row(); ?>
                <div class="details-grid-item">
                    <?php if($image = get_sub_field('icon')): ?>
                    <img src="<?php echo $image['url']; ?>" alt="<?php echo $image['alt']; ?>" loading="lazy">
                    <?php endif; ?>

                    <?php if(get_sub_field('title')): ?>
                    <h3><?php the_sub_field('title'); ?></h3>
                    <?php endif; ?>

                    <?php if(get_sub_field('content')): ?>
                        <?php the_sub_field('content'); ?>
                    <?php endif; ?>

                    <?php if($button = get_sub_field('button')): ?>
                    <a class="button-light-outline" href="<?php echo $button['url']; ?>" target="<?php echo $button['target'] ? $button['target'] : '_self'; ?>">
                        <?php echo $button['title']; ?>
                    </a>
                    <?php endif; ?>
                </div>
                <?php endwhile; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
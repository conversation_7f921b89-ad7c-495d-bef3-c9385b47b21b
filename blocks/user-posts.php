<?php 
    $author = get_field('user', get_the_ID());
    $name = get_the_title(get_the_ID());

    $title = sprintf(__('%s\' Posts', 'mm-smy'), substr($name, 0, strpos($name, ' ')));

    $args = array(
        'post_type' => 'post',
        'posts_per_page' => get_field('limit_posts'),
        'orderby' => 'rand',
        'author' => $author,
    );

    $query = new WP_Query($args);
?>
<?php if( !empty($author) && $query->have_posts() ): ?>

<div id="<?php echo theme_block_id($block); ?>" class="<?php echo theme_block_class($block, 'posts-cards alignfull'); ?>">
    <div class="container">
        <div class="posts-cards-inner" data-animation>
            <?php
                if(get_field('title')){
                    $title = get_field('title');
                }
            ?>
            <h2>
                <?php if(get_field('eyebrow')): ?>
                <span class="subtitle">
                    <?php the_field('eyebrow'); ?>
                </span>
                <?php endif; ?>

                <?php echo $title; ?>
            </h2>

            <div class="posts-cards-posts" data-posts="<?php the_field('limit_posts'); ?>">
                <?php while ($query->have_posts()) : $query->the_post(); ?>
                <?php get_template_part('part/post'); ?>
                <?php endwhile; ?>
            </div>            

            <?php if($button = get_field('button')): ?>
            <a class="button-light-filled" href="<?php echo $button['url']; ?>" target="<?php echo $button['target'] ? $button['target'] : '_self'; ?>">
                <?php echo $button['title']; ?>
            </a>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php endif; ?>
<?php wp_reset_postdata(); ?>
<div id="<?php echo theme_block_id($block); ?>" class="<?php echo theme_block_class($block, 'styled-background alignfull'); ?>">
    <?php if(get_field('background_color') != 'transparent'): ?>
    <div class="styled-background-color" data-color="<?php the_field('background_color'); ?>" data-size="<?php the_field('background_size'); ?>"></div>
    <?php endif; ?>

    <?php if(get_field('display_graphic')): ?>
    <div class="styled-background-graphic" data-side="<?php the_field('graphic_side'); ?>" data-position="<?php the_field('graphic_position'); ?>">
        <?php
            if(get_field('graphic_side') == 'left'){
                theme_asset('img/background-graphic-left.svg');
            }elseif(get_field('graphic_side') == 'right'){
                theme_asset('img/background-graphic-right.svg');
            }
        ?>
    </div>
    <?php endif; ?>

    <div class="blocks-container <?php the_field('vertical_space'); ?>">
        <InnerBlocks />
    </div>
</div>
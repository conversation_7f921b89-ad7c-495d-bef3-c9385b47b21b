<div id="<?php echo theme_block_id($block); ?>" class="<?php echo theme_block_class($block, 'team-members alignfull'); ?>">
    <div class="container">

        <?php if(get_field('enable_filters')): ?>
        <form class="team-members-filter" method="get">
            <?php
            $team_ids = get_field('team_members');
            $featured_ids = get_field('featured_team_members');
            ?>

            <?php if (!empty($team_ids)) : ?>
                <?php foreach ($team_ids as $id): ?>
                    <input type="hidden" name="team_members[]" value="<?php echo esc_attr($id); ?>">
                <?php endforeach; ?>
            <?php endif; ?>

            <?php if (!empty($featured_ids)) : ?>
                <?php foreach ($featured_ids as $id): ?>
                    <input type="hidden" name="featured_team_members[]" value="<?php echo esc_attr($id); ?>">
                <?php endforeach; ?>
            <?php endif; ?>

            <?php
                $positions = get_terms(array(
                    'taxonomy' => 'position',
                    'hide_empty' => true,
                ));
            ?>
            <?php if(!empty($positions)): ?>
            <select name="position">
                <option value=""><?php _e('Position', 'mm-smy'); ?></option>
                <?php foreach($positions as $term): ?>
                <option value="<?php echo esc_attr($term->slug); ?>" <?php if(isset($_GET['position']) && $term->slug == $_GET['position']) echo 'selected'; ?>>
                    <?php echo $term->name; ?>
                </option>
                <?php endforeach; ?>
            </select>
            <?php endif; ?>

            <?php
                $locations = get_terms(array(
                    'taxonomy' => 'location',
                    'hide_empty' => true,
                ));
            ?>
            <?php if(!empty($locations)): ?>
            <select name="location">
                <option value=""><?php _e('Location', 'mm-smy'); ?></option>
                <?php foreach($locations as $term): ?>
                <option value="<?php echo esc_attr($term->slug); ?>" <?php if(isset($_GET['location']) && $term->slug == $_GET['location']) echo 'selected'; ?>>
                    <?php echo $term->name; ?>
                </option>
                <?php endforeach; ?>
            </select>
            <?php endif; ?>

            <?php
                $services = get_terms(array(
                    'taxonomy' => 'service',
                    'hide_empty' => true,
                ));
            ?>
            <?php if(!empty($services)): ?>
            <select name="service">
                <option value=""><?php _e('Services', 'mm-smy'); ?></option>
                <?php foreach($services as $term): ?>
                <option value="<?php echo esc_attr($term->slug); ?>" <?php if(isset($_GET['service']) && $term->slug == $_GET['service']) echo 'selected'; ?>>
                    <?php echo $term->name; ?>
                </option>
                <?php endforeach; ?>
            </select>
            <?php endif; ?>

            <div class="team-members-search-wrapper">
                <span class="team-members-search-icon"></span>
                <input 
                    type="text" 
                    name="s" 
                    value="<?php echo esc_attr($_GET['s'] ?? ''); ?>" 
                    placeholder="<?php esc_attr_e('Search by name...', 'mm-smy'); ?>" 
                    class="team-members-search-input"
                />
            </div>
        </form>
        <?php endif; ?>

        <div class="team-members-loading">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid">
                <circle cx="50" cy="50" r="32" stroke-width="8" stroke="#023b64" stroke-dasharray="50.26548245743669 50.26548245743669" fill="none" stroke-linecap="round">
                    <animateTransform attributeName="transform" type="rotate" repeatCount="indefinite" dur="1s" keyTimes="0;1" values="0 50 50;360 50 50"></animateTransform>
                </circle>
            </svg>
        </div>

        <div class="team-members-inner">
        <?php
            $args = array(
                'post_type' => 'team_member',
                'posts_per_page' => -1,
                'orderby' => 'title',
                'order' => 'ASC',
            );

            if (!empty($_GET['s'])) {
                $args['s'] = sanitize_text_field($_GET['s']);
                $args['relevanssi'] = true;
                $args['orderby'] = 'relevance';
            }

            if(!empty($_GET['position'])){
                $args['tax_query'][] = array(
                    'taxonomy' => 'position',
                    'field' => 'slug',
                    'terms' => $_GET['position'],
                );
            }

            if(!empty($_GET['location'])){
                $args['tax_query'][] = array(
                    'taxonomy' => 'location',
                    'field' => 'slug',
                    'terms' => $_GET['location'],
                );
            }

            if(!empty($_GET['service'])){
                $args['tax_query'][] = array(
                    'taxonomy' => 'service',
                    'field' => 'slug',
                    'terms' => $_GET['service'],
                );
            }

            if(get_field('team_members')){
                $args['post__in'] = get_field('team_members');
                $args['orderby'] = 'post__in';
            }

            if(get_field('featured_team_members')){
                $featured_args = $args;
                $featured_args['post__in'] = get_field('featured_team_members');
                $featured_args['orderby'] = 'post__in';

                $args["post__not_in"] = get_field('featured_team_members');
            }
        ?>
        <?php $featured_query = new WP_Query($featured_args); ?>
        <?php if( !empty($featured_query) && $featured_query->have_posts() ): ?>
            <?php while ($featured_query->have_posts()) : $featured_query->the_post(); ?>
                <?php get_template_part('part/team-member-card'); ?>
            <?php endwhile; ?>
        <?php endif; ?>
        <?php wp_reset_postdata(); ?>

        <?php $query = new WP_Query($args); ?>
        <?php if( $query->have_posts() ): ?>
            <?php while ($query->have_posts()) : $query->the_post(); ?>
                <?php get_template_part('part/team-member-card'); ?>
            <?php endwhile; ?>
        <?php endif; ?>
        </div>
        <?php wp_reset_postdata(); ?>
    </div>
</div>
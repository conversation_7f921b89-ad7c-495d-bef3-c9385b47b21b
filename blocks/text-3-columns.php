<div id="<?php echo theme_block_id($block); ?>" class="<?php echo theme_block_class($block, 'text-3-columns alignfull'); ?>">
    <div class="container">
        <?php if(get_field('title') || get_field('eyebrow')): ?>
        <h2 data-animation>
            <?php if(get_field('eyebrow')): ?>
            <span class="subtitle">
                <?php the_field('eyebrow'); ?>
            </span>
            <?php endif; ?>

            <?php the_field('title'); ?>
        </h2>
        <?php endif; ?>

        <?php if(have_rows('columns')): ?>
        <div class="text-3-columns-inner">
            <?php while(have_rows('columns')): the_row(); ?>
            <div class="text-3-columns-column" data-animation>
                <?php if(get_sub_field('title')): ?>
                <h3><?php the_sub_field('title'); ?></h3>
                <?php endif; ?>

                <?php if(get_sub_field('content')): ?>
                <p><?php the_sub_field('content'); ?></p>
                <?php endif; ?>

                <?php if($button = get_sub_field('button')): ?>
                <a href="<?php echo $button['url']; ?>" target="<?php echo $button['target'] ? $button['target'] : '_self'; ?>">
                    <?php echo $button['title']; ?>
                </a>
                <?php endif; ?>
            </div>
            <?php endwhile; ?>
        </div>
        <?php endif; ?>
    </div>
</div>
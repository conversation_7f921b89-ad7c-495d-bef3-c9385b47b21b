<div id="<?php echo theme_block_id($block); ?>" class="<?php echo theme_block_class($block, 'posts-cards alignfull'); ?>">
    <div class="container">
        <div class="posts-cards-inner" data-animation>
            <?php if(get_field('title') || get_field('eyebrow')): ?>
            <h2>
                <?php if(get_field('eyebrow')): ?>
                <span class="subtitle">
                    <?php the_field('eyebrow'); ?>
                </span>
                <?php endif; ?>

                <?php the_field('title'); ?>
            </h2>
            <?php endif; ?>

            <?php 
                $args = array(
                    'post_type' => get_post_type(),
                    'posts_per_page' => get_field('limit_posts'),
                    'orderby' => 'rand',
                    'post__not_in' => array(get_the_ID()),
                );

                $query = new WP_Query($args);
            ?>
            <?php if( $query->have_posts() ): ?>
            <div class="posts-cards-posts" data-posts="<?php the_field('limit_posts'); ?>">
                <?php while ($query->have_posts()) : $query->the_post(); ?>
                <?php get_template_part('part/post'); ?>
                <?php endwhile; ?>
            </div>            
            <?php endif; ?>
            <?php wp_reset_postdata(); ?>

            <?php if($button = get_field('button')): ?>
            <a class="button-light-filled" href="<?php echo $button['url']; ?>" target="<?php echo $button['target'] ? $button['target'] : '_self'; ?>">
                <?php echo $button['title']; ?>
            </a>
            <?php endif; ?>
        </div>
    </div>
</div>
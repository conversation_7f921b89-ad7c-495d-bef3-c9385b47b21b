<?php get_header(); ?>

<?php while ( have_posts() ) : the_post(); ?>

<main id="page" class="default-template team-member-template">
    <div class="container">
        <ul class="breadcrumbs">
            <li>
                <a href="<?php echo esc_url(get_home_url()); ?>">
                    <?php _e('Home', 'mm-smy'); ?>
                </a>
            </li>
            <?php
                $ancestors = get_post_ancestors(get_field('team_members_archive_page', 'option'));
                if(!empty($ancestors)){
                    foreach($ancestors as $ancestor){
                        echo '<li><a href="'.get_the_permalink($ancestor).'">'.get_the_title($ancestor).'</a></li>';
                    }
                }
            ?>
            <li>
                <a href="<?php echo get_permalink(get_field('team_members_archive_page', 'option')); ?>">
                    <?php echo get_the_title(get_field('team_members_archive_page', 'option')); ?>
                </a>
            </li>
            <li>
                <?php the_title(); ?>
            </li>
        </ul>

        <article class="team-member-inner">
            <div class="team-member-inner-info">
                <?php if(has_post_thumbnail()): ?>
                <div class="team-member-inner-image">
                    <?php the_post_thumbnail('full'); ?>
                </div>
                <?php endif; ?>

                <?php the_title('<h1>', '</h1>'); ?>

                <?php if ($cta = get_field('cta_section')): ?>
                    <div class="team-member-cta">
                        <?php if (!empty($cta['cta_title'])): ?>
                            <h2><?php echo esc_html($cta['cta_title']); ?></h2>
                        <?php endif; ?>

                        <?php if (!empty($cta['cta_subtitle'])): ?>
                            <p><?php echo esc_html($cta['cta_subtitle']); ?></p>
                        <?php endif; ?>

                        <?php
                        $default_cta_link = [
                            'url'   => home_url('/contact-us/'),
                            'title' => __('Connect with us', 'mm-smy'),
                            'target' => ''
                        ];

                        $link_data = $cta['cta_link'] ?? null;

                        if (!empty($link_data['url']) && !empty($link_data['title'])) {
                            $cta_link = $link_data;
                        } else {
                            $cta_link = $default_cta_link;
                        }
                        ?>

                        <a class="button button-light-filled"
                        href="<?php echo esc_url($cta_link['url']); ?>"
                        <?php if (!empty($cta_link['target'])) echo 'target="' . esc_attr($cta_link['target']) . '"'; ?>>
                            <?php echo esc_html($cta_link['title']); ?>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
            <div class="team-member-inner-bio">
                <?php the_title('<h1>', '</h1>'); ?>

                <?php
                $manual_position = get_field('position');
                $experience = get_field('experience');

                if ($manual_position) {
                    $position_text = $manual_position;
                } else {
                    $positions = get_the_terms(get_the_ID(), 'position');
                    $position_text = !empty($positions) ? implode(' & ', wp_list_pluck($positions, 'name')) : '';
                }

                $output = array_filter([$position_text, $experience]);
                ?>

                <?php if (!empty($output)): ?>
                    <p class="team-member-inner-position">
                        <?php echo implode(', ', $output); ?>
                    </p>
                <?php endif; ?>

                <?php if(get_field('disclaimer')): ?>
                <p class="team-member-inner-disclaimer">
                    <?php the_field('disclaimer'); ?>
                </p>
                <?php endif; ?>
                
                <div class="team-member-inner-contact-row">
                    <?php
                        $locations = get_the_terms(get_the_ID(), 'location');
                    ?>
                    <?php if(!empty($locations)): ?>
                        <div class="team-member-inner-location">
                            <?php theme_asset('img/map-marker.svg'); ?>
                            <span class="team-member-inner-label">
                                <?php echo implode(', ', wp_list_pluck($locations, 'name')); ?>
                            </span>
                        </div>
                    <?php endif; ?>

                    <ul class="social-media">
                        <?php if(get_field('email')): ?>
                        <li>
                            <a href="#" class="email-link" data-name="<?php the_title(); ?>">
                                <?php theme_asset('img/icon-email.svg'); ?>
                                <span><?php _e('Email', 'mm-smy'); ?></span>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if(get_field('linkedin')): ?>
                        <li>
                            <a href="<?php the_field('linkedin'); ?>" target="_blank">
                                <?php theme_asset('img/icon-linkedin.svg'); ?>
                                <span><?php _e('LinkedIn', 'mm-smy'); ?></span>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>

                <?php
                    /*
                    $services = get_the_terms(get_the_ID(), 'service');
                    if(!empty($services)): ?>
                        <p class="team-member-inner-service">
                            <?php echo implode(', ', wp_list_pluck($services, 'name')); ?>
                        </p>
                    <?php endif;
                    */
                ?>

                <?php if ($intro = get_field('featured_section')): ?>
                    <div class="team-member-intro">
                        <?php echo $intro; ?>
                    </div>
                <?php endif; ?>

                <?php the_content(); ?>

                <?php if (have_rows('speaking_engagements')): ?>
                    <div class="team-member-speaking">
                        <h3><?php _e('Speaking Engagements', 'mm-smy'); ?></h3>
                        <ul>
                            <?php while (have_rows('speaking_engagements')): the_row(); ?>
                                <li><?php the_sub_field('title'); ?></li>
                            <?php endwhile; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php if ($insights = get_field('featured_insights')): ?>
                    <div class="team-member-insights">
                        <h3><?php _e('Featured Insights', 'mm-smy'); ?></h3>
                        <ul>
                            <?php foreach ($insights as $post): setup_postdata($post); ?>
                                <li><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></li>
                            <?php endforeach; wp_reset_postdata(); ?>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>
        </article>
    </div>

    <div class="blocks-container">
        <?php get_template_part('part/widgets', 'team_members'); ?>
    </div>
</main>

<?php endwhile; ?>

<?php get_footer();
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
	<meta charset="<?php bloginfo( 'charset' ); ?>" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <?php wp_head(); ?>
</head>
<body <?php body_class(); ?>>
<?php wp_body_open(); ?>
<a href="#main-content" class="hidden"><?php echo __('Go to content', 'mm-smy'); ?></a>
<div class="page-wrapper">

    <header class="header">
        <?php if(theme_get_menu_items('topbar_menu')): ?>
        <div class="topbar-header">
            <div class="container">
                <div class="top-search-bar">
                    <form role="search" method="get" class="search-form" action="<?php echo home_url( '/' ); ?>">
                        <input type="search" name="s" placeholder="<?php esc_attr_e( 'What are you looking for?', 'mm-smy' ); ?>" value="<?php echo get_search_query(); ?>" aria-label="<?php esc_attr_e( 'Search', 'mm-smy' ); ?>" />
                    </form>
                </div>
                <?php theme_menu('topbar_menu', 1); ?>
            </div>
        </div>
        <?php endif; ?>

        <div class="main-header">
            <div class="container">
                <div class="main-header-inner">
                    <?php if($logo = get_field('header_logo', 'option')): ?>
                    <a href="<?php echo esc_url(get_home_url()); ?>" class="logo">
                        <img src="<?php echo $logo['url']; ?>" alt="<?php echo get_bloginfo('name'); ?>" width="189" height="52">
                    </a>
                    <?php endif; ?>

                    <button class="main-menu-toggle toggle-menu" aria-label="<?php _e('Menu', 'mm-smy'); ?>">
                        <span class="menu-bar"></span>
                        <span class="menu-bar"></span>
                        <span class="menu-bar"></span>
                    </button>

                    <nav>
                        <?php theme_menu('main_menu', 3); ?>
                        <?php if(theme_get_menu_items('topbar_menu')): ?>
                            <div class="mobile-search">
                                <div class="topbar-header">
                                    <div class="container">
                                        <div class="top-search-bar">
                                            <form role="search" method="get" class="search-form" action="<?php echo home_url( '/' ); ?>">
                                                <input type="search" name="s" placeholder="<?php esc_attr_e( 'What are you looking for?', 'mm-smy' ); ?>" value="<?php echo get_search_query(); ?>" aria-label="<?php esc_attr_e( 'Search', 'mm-smy' ); ?>" />
                                            </form>
                                        </div>
                                        <?php theme_menu('topbar_menu', 1); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </nav>
                </div>
            </div>
        </div>
    </header>
    <script>
        // Set the header height variable. 
        // Placed here to avoid layout shift.
        let height = document.querySelector('header.header').clientHeight;
        document.querySelector(':root').style.setProperty('--header-height', height+'px');
    </script>

    <?php if($button = get_field('floating_cta', 'option')): ?>
    <a class="floating-cta" href="<?php echo $button['url']; ?>" target="<?php echo $button['target'] ? $button['target'] : '_self'; ?>">
        <?php echo $button['title']; ?>
    </a>
    <?php endif; ?>
    
    <div id="main-content"></div>
    